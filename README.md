# ray oauth登录模块
oauth登录模块，参照oauth规范的实现，所以接口访问方式与参数也遵守oauth规范  

网关层登录地址，请咨询相关管理员.

按照规范，只支持POST方式请求。

## 如何接入
告知管理员，应用的情况，协定登录模式，scope范围，确认access_token与refresh_token的过期时间  
管理员维护应用，分配clientId与clientSecret

## 全局参数说明

### header
- 登录时，按照oauth规范，header传递`Authorization: Basic client`参数  
- 其中client为`clientId:clientSecret`经过base64得到的数据
- clientId, clientSecret具体值，请咨询管理员
- Content-Type: application/x-www-form-urlencoded

### scope(request body中)
- 每个应用有各自的可用scope，一般有`all,read,write,delete`三个可选
- read 只能通过get方式访问
- 只有all,delete可支持delete方式访问

## client-credentials 模式
- 适用于作文提分等外部程序访问，直接服务器间授信，不需要关联当前用户
- header 按如上传入，同时requestBody中传入`grant_type=client_credentials&scope=all`
```
curl -X POST \
   http://lu-uat.fangxiao.top/oauth/token \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Authorization: Basic ZG9vcktlZXBlcjoxMjM0NTY=' \
  -d 'grant_type=client_credentials&scope=all'
```

## 密码模式
- 适用于管理后台等通过手机号或者用户名 密码方式的应用
- header 见上面描述`全局参数之 header`
- request body 参数说明
  - grant_type=password
  - username, mobile分别对应用户名/手机号两种登录模式，优先username。按需二选一传入。
  - password必填  
  - scope必填,具体参见上面全局参数中的描述
  - identity可不填， 合法值`teacher`和`admin`

参考curl如下：

```
用户名登录： 
curl -X POST \
   http://lu-uat.fangxiao.top/login/oauth/token \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Authorization: Basic ankyOjEyMzQ1Ng==' \
  -d 'username=13298168055&password=123456&grant_type=password&scope=all%2Cread1'
```

```
手机号登录：  
curl -X POST \
   http://lu-uat.fangxiao.top/login/oauth/token \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Authorization: Basic RkUtQURNSU46MTIzNDU2' \
  -d 'grant_type=password&password=123456&mobile=12345678901&scope=all&identity=teacher'
```

## 微信code模式
- 适用于公众号，小程序等登录
- header 见上面描述`全局参数之 header`
- header 额外增加`r-wechat-application-code`,具体值问管理员
- request body 参数说明
  - grant_type=code
  - scope必填,具体参见上面全局参数中的描述
  - identity可不填， 合法值`teacher`和`admin`
 
 返回结果说明：
 - 假如已绑定用户，则返回token。通过token获取用户信息
 - 假如未绑定，则返回401，`error.wechat.unbind`,extra_message为json，解析openId，继续调用用户名密码，完成登录  
 
## refresh_token模式
- 适用于access_token过期后，用户无感知获取新的access_token
- header 见上面描述`全局参数之 header`
- request body 参数说明
  - grant_type=refresh_token
  - refresh_token 传入登录时返回的refresh_token
- 返回内容与正常登录一致
- 并非所有应用都支持此模式，需管理员开通

## 返回示例
```
access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE1NzIyMzAxNjMsInVzZXJJZCI6NjYwMDAwMDAwMDQsInVzZXJfbmFtZSI6IjE5OTM3MTI4ODc5IiwianRpIjoiOTU3YTVlZmItMTNkZS00NGYyLWFiM2EtNTdlNjRmNDUxMzdmIiwiY2xpZW50X2lkIjoiZmUtYWRtaW4iLCJzY29wZSI6WyJhbGwiXX0.LdjdN3EeEqsHuLW4kH-_nn83zvCUqjyQOewANB1t8As"
refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE1NzI5MDU4NTcsInVzZXJfbmFtZSI6InBhcmVudDEiLCJqdGkiOiIzYWM5YjgyMi05ODI0LTQ5YjItOWMwNS1jODI0MWQ3YzM4NDAiLCJjbGllbnRfaWQiOiJsdSIsInNjb3BlIjpbImFsbCJdLCJhdGkiOiJjNTg0ZjM2OC1iZWMxLTQwY2MtOTU0Ni1jNTc1M2ExZDIyZTcifQ.fY8AQyOB3pk3PeTZ2DWePQBRaom-c7Xh6VhMz32XGWY"
expires_in: 7199
jti: "957a5efb-13de-44f2-ab3a-57e64f45137f"
scope: "all"
token_type: "bearer"
userId: 66000000004
```

## 资源服务访问方式
header中增加参数，格式为 'Authorization: Bearer {access_token}'

## 各应用建议
妥善保存refresh_token,用于access_token过期后，无需用户登录继续访问服务  

方案1： 根据`expires_in`确认具体过期时间，在过期前，根据refresh_token获取新的access_token  

方案2： 统一封装请求，返回401时，根据refresh_token获取新的access_token  


