package com.jysd.ray.oauth.lib.config.jackson;

import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "@class")
@com.fasterxml.jackson.databind.annotation.JsonSerialize(using = CustomOAuth2AccessTokenJackson2Serializer.class)
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = CustomOAuth2AccessTokenJackson2Deserializer.class)
public abstract class CustomOAuth2AccessTokenMixin {
}
