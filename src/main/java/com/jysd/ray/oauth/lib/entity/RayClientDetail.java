package com.jysd.ray.oauth.lib.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.util.StringUtils;

import java.util.*;

@Setter
@EqualsAndHashCode(callSuper = false)
@TableName("sys_client")
public class RayClientDetail implements ClientDetails {

    @TableId
    @Getter
    private String clientId;
    @Getter
    private String clientSecret;
    private String scope;
    private String resourceIds;
    private String authorizedGrantTypes;
    @TableField(value = "web_server_redirect_uri")
    private String registeredRedirectUris;
    @TableField(value = "autoapprove")
    private String autoApproveScopes;
    private String authorities;
    @TableField(value = "access_token_validity")
    @Getter
    private Integer accessTokenValiditySeconds;

    @Getter
    private String wechatApplicationCode;
    @TableField(value = "refresh_token_validity")
    @Getter
    private Integer refreshTokenValiditySeconds;

    private String additionalInformation;

    @Override
    public Set<String> getResourceIds() {
        if (StringUtils.hasText(resourceIds)) {
            Set<String> resources = StringUtils
                    .commaDelimitedListToSet(resourceIds);
            if (!resources.isEmpty()) {
                return resources;
            }
        }
        return null;
    }

    @Override
    public boolean isSecretRequired() {
        return this.clientSecret != null;
    }

    @Override
    public boolean isScoped() {
        return this.scope != null && !this.scope.isEmpty();
    }

    @Override
    public Set<String> getScope() {
        if (StringUtils.hasText(scope)) {
            Set<String> scopeList = StringUtils.commaDelimitedListToSet(scope);
            if (!scopeList.isEmpty()) {
                return scopeList;
            }
        }
        return null;
    }

    @Override
    public Set<String> getAuthorizedGrantTypes() {
        if (StringUtils.hasText(authorizedGrantTypes)) {
            return StringUtils
                    .commaDelimitedListToSet(authorizedGrantTypes);
        } else {
             return new HashSet<String>(Arrays.asList(
                    "authorization_code", "refresh_token"));
        }
    }

    @Override
    public Set<String> getRegisteredRedirectUri() {
        if (StringUtils.hasText(registeredRedirectUris)) {
              return StringUtils.commaDelimitedListToSet(registeredRedirectUris);
        }
        return null;
    }

    @Override
    public Collection<GrantedAuthority> getAuthorities() {
        if (StringUtils.hasText(authorities)) {
            return AuthorityUtils.commaSeparatedStringToAuthorityList(authorities);
        }
        return null;
    }


    @Override
    public boolean isAutoApprove(String scope) {
        if (autoApproveScopes == null) {
            return false;
        }
        for (String auto : StringUtils.commaDelimitedListToSet(autoApproveScopes)) {
            if (auto.equals("true") || scope.matches(auto)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Object> getAdditionalInformation() {
        Gson gson = new Gson();
        Map<String, Object> map = new HashMap<>();
        map = gson.fromJson(additionalInformation, new TypeToken<Map<String, Object>>() {}.getType());
        return map;
    }

}
