package com.jysd.ray.oauth.lib.config.jackson;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import org.springframework.security.oauth2.common.DefaultExpiringOAuth2RefreshToken;
import org.springframework.security.oauth2.common.DefaultOAuth2RefreshToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;

import java.io.IOException;
import java.util.Date;

public class CustomOAuth2RefreshTokenDeserializer extends StdDeserializer<OAuth2RefreshToken> {

    protected CustomOAuth2RefreshTokenDeserializer() {
        super(OAuth2RefreshToken.class);
    }

    @Override
    public OAuth2RefreshToken deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonNode root = p.getCodec().readTree(p);
        String value = root.path("value").asText();
        JsonNode expiration = root.path("expiration");
        if (expiration.isMissingNode()) {
            return new DefaultOAuth2RefreshToken(value);
        } else {
            //use the default objectMapper setting; date to long string;
            return new DefaultExpiringOAuth2RefreshToken(value, new Date(expiration.get(1).asLong()));
        }
    }

}