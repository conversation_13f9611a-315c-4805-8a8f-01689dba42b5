package com.jysd.ray.oauth.lib.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jysd.ray.oauth.lib.dto.RoleDto;
import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-23
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("account_user")
public class RayUser implements UserDetails {

    private static final long serialVersionUID = 1L;

    private String username;

    private String mobile;

    @TableField(value = "encrypted_password")
    private String password;

    private Integer identity;

    private Long defaultStudentSchoolId;

    private Long defaultTeacherSchoolId;

    @TableField(exist = false)
    private UserIdentityEnum currentIdentity;

    @TableField(exist = false)
    @Deprecated
    private Long currentSchoolId;

    private Boolean locked;

    private LocalDateTime deletedAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private transient LocalDateTime loginAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime resetPwdAt;

    private Long id;

    @TableField(exist = false)
    private List<Long> adminSchoolIds;

    @TableField(exist = false)
    private List<RoleDto> roleDtoList;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return !locked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
//        return resetPwdAt == null || (loginAt != null && loginAt.isAfter(resetPwdAt));
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    public void setLoginAt(LocalDateTime loginAt) {
        this.loginAt = loginAt == null ? LocalDateTime.now() : loginAt;
    }

    /**
     * 校验用户是否拥有当前登陆所用身份
     */
    public boolean hasIdentity() {
        int index = currentIdentity.getIndex();
        // currentIdentity其实是Identity二进制的倒数的位数
        if (index < 1) {
            throw new IllegalArgumentException("currentIdentity必须大于0");
        }
        int checkIndexBeginVal = identity >> (index - 1);
        return (checkIndexBeginVal & 1) == 1;
    }
}
