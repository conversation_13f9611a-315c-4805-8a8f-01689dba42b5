package com.jysd.ray.oauth.lib.dto;

import com.jysd.ray.oauth.lib.entity.RayUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserExpandDto extends RayUser {

    private String needBindMpAppCode;

    private Boolean bindResult;


}
