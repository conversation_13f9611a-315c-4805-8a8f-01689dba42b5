package com.jysd.ray.oauth.lib.config.jackson;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.TypeDeserializer;
import com.fasterxml.jackson.databind.node.MissingNode;
import com.jysd.ray.oauth.lib.entity.RayUser;
import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class RayUserDeserializer extends JsonDeserializer<RayUser> {

    public RayUserDeserializer() {
    }

    @Override
    public RayUser deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        ObjectMapper mapper = (ObjectMapper) jp.getCodec();
        JsonNode jsonNode = mapper.readTree(jp);
        JsonNode userId = readJsonNode(jsonNode, "id");
        JsonNode username = readJsonNode(jsonNode, "username");
        JsonNode mobile = readJsonNode(jsonNode, "mobile");
        JsonNode identity = readJsonNode(jsonNode, "identity");
        JsonNode currentIdentity = readJsonNode(jsonNode, "currentIdentity");
        JsonNode currentSchoolId = readJsonNode(jsonNode, "currentSchoolId");
        JsonNode password = readJsonNode(jsonNode, "password");
        JsonNode locked = readJsonNode(jsonNode, "locked");
        JsonNode loginAt = readJsonNode(jsonNode, "loginAt");
        JsonNode resetPwdAt = readJsonNode(jsonNode, "resetPwdAt");
        JsonNode adminSchoolIds = readJsonNode(jsonNode, "adminSchoolIds");

        RayUser result = RayUser.builder()
                .id(userId.asLong())
                .username(username.asText())
                .mobile(mobile.asText())
                .identity(identity.asInt())
                .currentSchoolId(currentSchoolId.isNull() ? null: currentSchoolId.asLong())
                .password(password.asText())
                .locked(locked.asBoolean())
                .build();
        if (!loginAt.asText().isEmpty() && !loginAt.asText().equals("null")) {
            result.setLoginAt(LocalDateTime.parse(loginAt.asText(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (!resetPwdAt.asText().isEmpty() && !resetPwdAt.asText().equals("null")) {
            result.setResetPwdAt(LocalDateTime.parse(resetPwdAt.asText(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (!currentIdentity.isNull()) {
            try {
                result.setCurrentIdentity(UserIdentityEnum.valueOf(currentIdentity.asText().toUpperCase()));
            } catch (Exception ignore) {
            }
        }
        //adminSchoolIds字段解析
        if (Objects.nonNull(adminSchoolIds) && !"null".equalsIgnoreCase(adminSchoolIds.asText())) {
            List<Long> adminSchoolIdList = new ArrayList<>();
            final JsonNode node = adminSchoolIds.get(1);
            for (JsonNode adminSchoolId : node) {
                adminSchoolIdList.add(adminSchoolId.asLong());
            }
            result.setAdminSchoolIds(adminSchoolIdList);
        }
        return result;
    }

    @Override
    public Object deserializeWithType(JsonParser p, DeserializationContext ctxt,
                                      TypeDeserializer typeDeserializer) throws IOException {
        return deserialize(p, ctxt);
    }

    private JsonNode readJsonNode(JsonNode jsonNode, String field) {
        return jsonNode.has(field) ? jsonNode.get(field) : MissingNode.getInstance();
    }
}
