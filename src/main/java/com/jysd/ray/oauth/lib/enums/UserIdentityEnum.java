package com.jysd.ray.oauth.lib.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

public enum UserIdentityEnum {
    STUDENT(1, "学生"),
    PARENT(2, "家长"),
    SCHOOL(3, "学校工作人员"),
    ADMIN(4, "管理员"),
    SUPER_ADMIN(5, "超级管理员"),
    SUPPLIER(6, "供应商"),
    GOVERNMENT(7, "政府"),
    PRISON(8, "监狱人员"),
    OTHER(9, "其他");


    @EnumValue
    @Getter
    private final int index;
    @Getter
    private final String desc;

    UserIdentityEnum(int index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public static UserIdentityEnum getByIndex(Integer i) {
        if (i == null) return null;
        for(UserIdentityEnum u : UserIdentityEnum.values()) {
            if (u.getIndex() == i) {
                return u;
            }
        }
        return null;
    }
}
