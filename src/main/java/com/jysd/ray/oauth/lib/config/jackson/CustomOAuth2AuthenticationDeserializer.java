package com.jysd.ray.oauth.lib.config.jackson;

import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;

import java.io.IOException;

public class CustomOAuth2AuthenticationDeserializer extends JsonDeserializer<OAuth2Authentication> {

    @Override
    public OAuth2Authentication deserialize(com.fasterxml.jackson.core.JsonParser jp, com.fasterxml.jackson.databind.DeserializationContext ctxt) throws IOException, com.fasterxml.jackson.core.JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) jp.getCodec();
        JsonNode jsonNode = mapper.readTree(jp);
        JsonNode storedRequest = jsonNode.get("storedRequest");
        JsonNode userAuthenticationNode = jsonNode.get("userAuthentication");
        JsonNode detailsNode = jsonNode.get("details");
        OAuth2Request request = null;
        if (storedRequest != null && !storedRequest.isNull()) {
            request = mapper.readValue(storedRequest.traverse(mapper),OAuth2Request.class);
        }
        Authentication auth = null;
        if (userAuthenticationNode != null && !userAuthenticationNode.isNull()) {
            String className = userAuthenticationNode.get("@class").asText();
            if ("org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken".equals(className)) {
                auth = mapper.readValue(userAuthenticationNode.traverse(mapper), PreAuthenticatedAuthenticationToken.class);
            } else {
                auth = mapper.readValue(userAuthenticationNode.traverse(mapper), UsernamePasswordAuthenticationToken.class);
            }
        }
        OAuth2Authentication token = new OAuth2Authentication(request, auth);
        if (detailsNode != null && !detailsNode.isNull()) {
            OAuth2AuthenticationDetails authenticationDetails = mapper.readValue(detailsNode.traverse(mapper),OAuth2AuthenticationDetails.class);
            token.setDetails(authenticationDetails);
        }
        return token;
    }
}
