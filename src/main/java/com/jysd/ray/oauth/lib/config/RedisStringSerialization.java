package com.jysd.ray.oauth.lib.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jysd.ray.oauth.lib.config.jackson.*;
import com.jysd.ray.oauth.lib.entity.RayUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.jackson2.CoreJackson2Module;
import org.springframework.security.oauth2.common.DefaultExpiringOAuth2RefreshToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.store.redis.StandardStringSerializationStrategy;
import org.springframework.security.web.jackson2.WebJackson2Module;
import org.springframework.security.web.jackson2.WebServletJackson2Module;
import org.springframework.security.web.savedrequest.DefaultSavedRequest;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

@Slf4j
public class RedisStringSerialization extends StandardStringSerializationStrategy {

    @Override
    protected <T> T deserializeInternal(byte[] bytes, Class<T> clazz) {
        GenericJackson2JsonRedisSerializer serializer = buildSerializer();
        return serializer.deserialize(bytes, clazz);
    }

    @Override
    protected byte[] serializeInternal(Object object) {
        GenericJackson2JsonRedisSerializer serializer = buildSerializer();
        return serializer.serialize(object);
    }

    private ObjectMapper buildMapper() {
        MappingJackson2JsonView mapping = new MappingJackson2JsonView();
        ObjectMapper mapper = mapping.getObjectMapper();
        mapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
//        mapper.disable(MapperFeature.AUTO_DETECT_SETTERS);
        // register应放在前面，否则后面自定义的不生效
        mapper.registerModule(new CoreJackson2Module());
//        mapper.registerModule(new CasJackson2Module());
        mapper.registerModule(new WebJackson2Module());
        mapper.registerModule(new WebServletJackson2Module());
        mapper.addMixIn(OAuth2AccessToken.class, CustomOAuth2AccessTokenMixin.class);
        mapper.addMixIn(OAuth2Authentication.class, CustomOAuth2AuthenticationMixin.class);
        mapper.addMixIn(OAuth2Request.class, CustomOAuth2RequestMixin.class);
        mapper.addMixIn(DefaultExpiringOAuth2RefreshToken.class, CustomOAuth2RefreshTokenMixin.class);
        mapper.addMixIn(DefaultSavedRequest.class, DefaultSavedRequestMixin.class);
        mapper.addMixIn(UsernamePasswordAuthenticationToken.class, CustomUsernamePasswordAuthenticationTokenMixin.class);
        mapper.addMixIn(RayUser.class, RayUserMixin.class);
        return mapper;
    }

    private GenericJackson2JsonRedisSerializer buildSerializer() {
        return new GenericJackson2JsonRedisSerializer(buildMapper());
    }
}
