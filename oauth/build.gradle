application {
    mainClassName("com.jysd.ray.oauth.OauthApplication")
}

version "1.1.31"

ext {
    dockerRepo = 'harbor.ijx.icu'
    if (!project.hasProperty('dockerPrefix')) {
        dockerPrefix = 'ijx'
    }
    if (!project.hasProperty('branchName')) {
        set("branchName", 'dev')
    }
}

dependencies {
    compile project(':lib-oauth')
    compile 'com.jysd.ray.lib:ray-exception:1.4.8'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-oauth2'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation "com.ctrip.framework.apollo:apollo-client:1.5.0"
    implementation 'com.baomidou:mybatis-plus-boot-starter:3.2.0'
    implementation 'com.alibaba:druid-spring-boot-starter:1.1.20'
    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'
    runtimeOnly "org.springframework.boot:spring-boot-devtools"
    runtimeOnly 'mysql:mysql-connector-java:8.0.16'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

bootJar {
    launchScript()
    archiveName "ray-oauth.jar"
}
dockerPrepare.dependsOn(bootJar)
docker {
    name "${dockerRepo}/${dockerPrefix}/ray-oauth-${branchName}:${version}"
    tag 'latest', "ray-oauth-${branchName}:${version}"
    dockerfile file('Dockerfile')
    copySpec.from("build/libs").into("./")
    buildArgs([BUILD_VERSION: 'version'])
}