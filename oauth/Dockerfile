FROM registry.cn-beijing.aliyuncs.com/ijx-public/opentelemetry-javaagent:1.26.0 as opentelemetry
FROM mcr.microsoft.com/java/jre:11u4-zulu-alpine
RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
VOLUME /tmp
ADD ray-oauth.jar ray-oauth.jar
COPY --from=opentelemetry / /opentelemetry
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/ray-oauth.jar"]