package com.jysd.ray.oauth.param;

import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegistParam {

    private String name;

    private String username;

    private String code;

    private String mobile;

    private String password;

    private String openid;

    /**
     * 此值不传递
     */
    @Builder.Default
    private String userIdentity = UserIdentityEnum.PARENT.toString();
}
