package com.jysd.ray.oauth.config.oauth;

import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.dto.WechatMaLoginDto;
import com.jysd.ray.oauth.enums.PrincipleTypeEnum;
import com.jysd.ray.oauth.feign.WechatFeign;
import com.jysd.ray.oauth.lib.entity.RayClientDetail;
import com.jysd.ray.oauth.lib.entity.RayUser;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import com.jysd.ray.oauth.service.impl.UserDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.*;
import org.springframework.security.oauth2.common.exceptions.InvalidRequestException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class CustomWechatCodeTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "wechat_code";
    private WechatFeign wechatFeign;
    private UserDetailServiceImpl userDetailsService;
    private UserAuthenticationCommonService userAuthenticationCommonService;

    public CustomWechatCodeTokenGranter(
            UserDetailServiceImpl userDetailsService,
            UserAuthenticationCommonService userAuthenticationCommonService,
            ClientDetailsService clientDetailsService,
            AuthorizationServerTokenServices tokenServices,
            WechatFeign wechatFeign,
            OAuth2RequestFactory requestFactory) {
        this(userDetailsService, userAuthenticationCommonService, clientDetailsService, tokenServices, wechatFeign, requestFactory, GRANT_TYPE);
    }

    protected CustomWechatCodeTokenGranter(UserDetailServiceImpl userDetailsService,
                                           UserAuthenticationCommonService userAuthenticationCommonService,
                                           ClientDetailsService clientDetailsService,
                                           AuthorizationServerTokenServices tokenServices,
                                           WechatFeign wechatFeign,
                                           OAuth2RequestFactory requestFactory,
                                           String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.wechatFeign = wechatFeign;
        this.userDetailsService = userDetailsService;
        this.userAuthenticationCommonService = userAuthenticationCommonService;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());

        String clientId = client.getClientId();
        parameters.put("clientId", clientId);
        String authorizationCode = parameters.get("code");
        if (authorizationCode == null) {
            throw new InvalidRequestException("An authorization code must be supplied.");
        }

        //校验微信应用是否真实存在
        RayClientDetail rayClientDetail = (RayClientDetail) client;
        userAuthenticationCommonService.checkWxAppConfig(rayClientDetail);

        WechatMaLoginDto wechatMaLoginDto = wechatFeign.login(authorizationCode, rayClientDetail.getWechatApplicationCode());
        log.debug("wechatMaLoginDto: {}", wechatMaLoginDto);
        if (null == wechatMaLoginDto) {
            throw new RestException("error.wechat.login-fail", "wechat 服务返回异常");
        } else if (null == wechatMaLoginDto.getUserId()) {
            throw new RestException(HttpStatus.UNAUTHORIZED, "error.wechat.unbind", wechatMaLoginDto.toString());
        }
        RayUser user = userDetailsService.getById(wechatMaLoginDto.getUserId());
        if (Objects.isNull(user)) {
            log.error("系统用户不存在,userId =" + wechatMaLoginDto.getUserId());
            throw new RestException("error.oauth.user-id-not-exist", wechatMaLoginDto.toString());
        }

        PrincipleDTO principleDTO = PrincipleDTO.builder()
                .principle(user.getId())
                .principleType(PrincipleTypeEnum.USER_ID)
                .build();
        PreAuthenticatedAuthenticationToken userAuth = new PreAuthenticatedAuthenticationToken(principleDTO, null);
        log.debug("userAuth: {}", userAuth);

        userAuth.setDetails(parameters);
        UsernamePasswordAuthenticationToken newAuth =
                userAuthenticationCommonService.authenticateUserInfo(userAuth);

        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, newAuth);

    }

}