package com.jysd.ray.oauth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 设备信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfoDto {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备类型（PC、Mobile、Tablet等）
     */
    private String deviceType;
    
    /**
     * 操作系统信息
     */
    private String osInfo;
    
    /**
     * 浏览器信息
     */
    private String browserInfo;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 登录时间
     */
    private LocalDateTime loginTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * Token过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 是否为当前设备
     */
    private Boolean isCurrent;
    
    /**
     * Token值（部分显示，用于标识）
     */
    private String tokenPreview;
}
