package com.jysd.ray.oauth.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * UserVo
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {
    private static final long serialVersionUID = 7620021275183936014L;

    private Long id;

    private String username;

    private String name;

    private String mobile;

    private String idCard;

    private Boolean locked;

    private Long defaultStudentId;

    private Long mainParentId;

    private Long defaultSchoolId;

    private Integer identity;

    private String ethnicGroup;
    private Date birthday;
    private String place;
    private String email;
    private String avatarUrl;

    private String idCardPicUrl;


    private String thirdId;

    private String thirdExtraId;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}