package com.jysd.ray.oauth.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.enums.ClientVerificationCodeEnum;
import com.jysd.ray.oauth.param.VerificationCodeParam;
import com.jysd.ray.oauth.service.VerificationCodeService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping(("/verification-code"))
public class VerificationCodeController {
    @Value("#{'${test-mobile:15038203788,15911053968}'.split(',')}")
    private List<String> testMobile;
    @Resource
    private VerificationCodeService verificationCodeService;

    @PostMapping
    public ResponseEntity sendVerificationCode(HttpServletRequest request, @Validated @RequestBody VerificationCodeParam param) {
        String clientId = request.getHeader("Client-Id");
        if (StrUtil.isBlank(clientId)) {
            throw new RestException("Client-Id不能为空");
        }

        ClientVerificationCodeEnum clientVerificationCodeEnum =
                ClientVerificationCodeEnum.ofClientId(clientId);
        if (CollUtil.isNotEmpty(testMobile) && testMobile.contains(param.getMobile())) {
            return ResponseEntity.ok(JSONUtil.createObj().set("success", true).set("verificationCode", "967111"));
        }
        String verificationCode = verificationCodeService.sendVerificationCode(param, clientVerificationCodeEnum);

        //添加返回值，兼容前端解析body为空报错问题
        return ResponseEntity.ok(JSONUtil.createObj().set("success", true).set("verificationCode", verificationCode));
    }
}
