package com.jysd.ray.oauth.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserSecretDto {

    private Long userId;

    private String secret;

    @Override
    public String toString() {
        return "{" +
                "userId=" + userId +
                ", secret='" + secret + '\'' +
                '}';
    }
}
