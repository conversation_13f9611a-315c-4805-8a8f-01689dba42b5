package com.jysd.ray.oauth.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SmsRequestDTO {

    private String appCode;
    // 手机号
    private String phoneNum;
    // 验证码短信类型 0:登录/注册,1:H5登录/注册,2:绑定手机号,3:修改手机号
    private int yzmType;
    // 待校验验证码
    private String toBeVerifiedYzm;

    private String templateId;

    private String extraData;

    private int isH5Scene;

    private String templateContentParam;

    private String templateContent;

    /*
     * 阿里滑块校验参数
     */
    private String sessionId;
    private String sig;
    private String token;
    private String scene;

}
