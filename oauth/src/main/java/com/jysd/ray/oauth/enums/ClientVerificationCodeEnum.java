package com.jysd.ray.oauth.enums;

import lombok.Getter;

@Getter
public enum ClientVerificationCodeEnum {

    MEAL_MANAGE_PLATFORM("meal-manager-platform", "QYL", "585840", "您的验证码为：{0}，请于{1}内正确输入，如非本人操作，请忽略此短信！");

    private String clientId;

    private String appCode;

    private String templateId;

    private String templateContent;



    ClientVerificationCodeEnum(String clientId, String appCode, String templateId, String templateContent) {
        this.clientId = clientId;
        this.appCode = appCode;
        this.templateId = templateId;
        this.templateContent = templateContent;
    }

    public static ClientVerificationCodeEnum ofClientId(String clientId) {
        for (ClientVerificationCodeEnum codeEnum : ClientVerificationCodeEnum.values()) {
            if (codeEnum.getClientId().equals(clientId)) {
                return codeEnum;
            }
        }
        return null;
    }
}
