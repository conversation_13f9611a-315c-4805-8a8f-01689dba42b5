package com.jysd.ray.oauth.config.oauth;

import cn.hutool.core.util.StrUtil;
import com.jysd.ray.oauth.dto.FeishuUserInfo;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.enums.PrincipleTypeEnum;
import com.jysd.ray.oauth.feign.FeishuFeign;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class FeishuCodeTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "feishu_code";

    private FeishuFeign feishuFeign;

    private UserAuthenticationCommonService userAuthenticationCommonService;

    public FeishuCodeTokenGranter(AuthorizationServerTokenServices tokenServices,
                                  UserAuthenticationCommonService userAuthenticationCommonService,
                                  ClientDetailsService clientDetailsService,
                                  FeishuFeign feishuFeign,
                                  OAuth2RequestFactory requestFactory) {
        this(tokenServices, userAuthenticationCommonService, clientDetailsService, feishuFeign, requestFactory,
                GRANT_TYPE);
    }

    protected FeishuCodeTokenGranter(AuthorizationServerTokenServices tokenServices,
                                     UserAuthenticationCommonService userAuthenticationCommonService,
                                     ClientDetailsService clientDetailsService,
                                     FeishuFeign feishuFeign, OAuth2RequestFactory requestFactory, String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.feishuFeign = feishuFeign;
        this.userAuthenticationCommonService = userAuthenticationCommonService;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());

        String code = parameters.get("code");
        FeishuUserInfo feishuUserInfo = feishuFeign.getUserInfoByCode(code);

        String clientId = client.getClientId();
        parameters.put("clientId", clientId);

        PrincipleDTO principleDTO = PrincipleDTO.builder()
                .principle(StrUtil.removePrefix(feishuUserInfo.getMobile(), "+86"))
                .principleType(PrincipleTypeEnum.MOBILE)
                .build();

        //校验用户基本信息
        UsernamePasswordAuthenticationToken userAuth = new UsernamePasswordAuthenticationToken(principleDTO, null);
        log.debug("userAuth: {}", userAuth);
        userAuth.setDetails(parameters);
        UsernamePasswordAuthenticationToken newAuth =
                userAuthenticationCommonService.authenticateUserInfo(userAuth);

        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, newAuth);
    }
}
