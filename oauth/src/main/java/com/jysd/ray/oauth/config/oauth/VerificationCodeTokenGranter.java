package com.jysd.ray.oauth.config.oauth;

import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.enums.ClientVerificationCodeEnum;
import com.jysd.ray.oauth.enums.PrincipleTypeEnum;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import com.jysd.ray.oauth.service.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class VerificationCodeTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "verification_code";
    
    private final VerificationCodeService verificationCodeService;
    private final UserAuthenticationCommonService userAuthenticationCommonService;
    private final Environment environment;

    public VerificationCodeTokenGranter(
            AuthorizationServerTokenServices tokenServices,
            ClientDetailsService clientDetailsService,
            OAuth2RequestFactory requestFactory,
            VerificationCodeService verificationCodeService,
            UserAuthenticationCommonService userAuthenticationCommonService,
            Environment environment) {
        super(tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
        this.verificationCodeService = verificationCodeService;
        this.userAuthenticationCommonService = userAuthenticationCommonService;
        this.environment = environment;
        log.info("VerificationCodeTokenGranter initialized with Environment: {}", environment);
    }

    private List<String> getTestMobiles() {
        String testMobileStr = environment.getProperty("test-mobile", "15038203788,15911053968");
        List<String> testMobiles = Arrays.asList(testMobileStr.split(","));
        log.info("Current test mobile numbers: {}", testMobiles);
        return testMobiles;
    }
    
    private String getTestCaptcha() {
        String testCaptcha = environment.getProperty("test-captcha", "967111");
        log.info("Current test captcha: {}", testCaptcha);
        return testCaptcha;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
        String mobile = parameters.get("mobile");
        String code = parameters.get("code");

        // 验证参数
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(code)) {
            throw new RestException(HttpStatus.BAD_REQUEST, "error.basic.param-invalid", "手机号和验证码不能为空");
        }

        String clientId = client.getClientId();
        parameters.put("clientId", clientId);


        // 方法2：如果一定要用旧版EnumUtils，可以先检查是否存在
        ClientVerificationCodeEnum clientVerificationCodeEnum = ClientVerificationCodeEnum.ofClientId(clientId);
        if (null == clientVerificationCodeEnum) {
            throw new RestException(HttpStatus.BAD_REQUEST, "error.basic.param-invalid", "无效的客户端");
        }

        // 动态获取测试手机号和验证码
        List<String> testMobiles = getTestMobiles();
        String testCaptcha = getTestCaptcha();
        
        if(testMobiles.contains(mobile)){
            log.info("测试手机号，{}, 验证码：{}", testMobiles, testCaptcha);
            if(!code.equals(testCaptcha)){
                throw new RestException("error.oauth.verification-code-invalid", "验证码错误或已过期!");
            }
        }else{
            // 验证验证码
            if (!verificationCodeService.verifyCode(mobile, code, clientVerificationCodeEnum)) {
                throw new RestException("error.oauth.verification-code-invalid", "验证码错误或已过期");
            }
        }

        // 构建认证信息
        PrincipleDTO principleDTO = PrincipleDTO.builder()
                .principle(mobile)
                .principleType(PrincipleTypeEnum.MOBILE)
                .build();

        UsernamePasswordAuthenticationToken userAuth = new UsernamePasswordAuthenticationToken(principleDTO, null);
        userAuth.setDetails(parameters);

        // 认证用户
        UsernamePasswordAuthenticationToken newAuth = userAuthenticationCommonService.authenticateUserInfo(userAuth);

        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, newAuth);
    }
}
