package com.jysd.ray.oauth.config;

import cn.hutool.core.collection.CollUtil;
import com.joinus.ray.lib.redis.util.AuthRedisUtil;
import com.jysd.ray.oauth.config.oauth.*;
import com.jysd.ray.oauth.exception.CustomWebResponseExceptionTranslator;
import com.jysd.ray.oauth.feign.AccountFeign;
import com.jysd.ray.oauth.feign.FeishuFeign;
import com.jysd.ray.oauth.feign.WechatFeign;
import com.jysd.ray.oauth.lib.config.RedisStringSerialization;
import com.jysd.ray.oauth.lib.dto.UserExpandDto;
import com.jysd.ray.oauth.service.ISchoolService;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import com.jysd.ray.oauth.service.VerificationCodeService;
import com.jysd.ray.oauth.service.impl.ClientDetailServiceImpl;
import com.jysd.ray.oauth.service.impl.UserDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.CompositeTokenGranter;
import org.springframework.security.oauth2.provider.OAuth2RequestFactory;
import org.springframework.security.oauth2.provider.TokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {

    @Autowired
    @Qualifier("authRedisConnectionFactory")
    private RedisConnectionFactory redisConnectionFactory;

    @Autowired
    private UserAuthenticationManager authenticationManager;

    @Autowired
    private ClientDetailServiceImpl clientDetailService;

    @Autowired
    private UserDetailServiceImpl userDetailService;

    @Autowired
    private UserAuthenticationCommonService userAuthenticationCommonService;

    @Autowired
    private WechatFeign wechatFeign;

    @Autowired
    private AccountFeign accountFeign;

    @Autowired
    private FeishuFeign feishuFeign;

    @Autowired
    private AuthRedisUtil authRedisUtil;

    @Autowired
    private BankJiAESConfig bankJiAESConfig;

    @Autowired
    private VerificationCodeService verificationCodeService;

    @Resource
    private ISchoolService schoolService;

    @Autowired
    private Environment environment;

    @Value("${ray-oauth.jwt.secret-key:dq80vb3gx7n5k9pl2m4js6h1wytz0rfc}")
    private String jwtSecretKey;

    @Bean
    public RedisTokenStore redisTokenStore() {
        RedisTokenStore redisTokenStore = new RedisTokenStore(redisConnectionFactory);
        redisTokenStore.setPrefix("OAUTH:");
        redisTokenStore.setSerializationStrategy(new RedisStringSerialization());
        return redisTokenStore;
    }

    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
        security.tokenKeyAccess("permitAll()").checkTokenAccess("isAuthenticated()");
    }

    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
        List<TokenGranter> tokenGranters = getTokenGranters(endpoints.getTokenServices(),
                endpoints.getOAuth2RequestFactory());
        //endpoints.getTokenGranter()就是 Spring OAuth2 默认提供的 TokenGranter，它包含了：
        //ClientCredentialsTokenGranter (client_credentials模式)
        //RefreshTokenGranter (refresh_token模式)
        //ImplicitTokenGranter (implicit模式)
        //AuthorizationCodeTokenGranter (authorization_code模式)
        //ResourceOwnerPasswordTokenGranter (password模式)
        tokenGranters.add(endpoints.getTokenGranter());

        TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
        tokenEnhancerChain.setTokenEnhancers(Arrays.asList(
                tokenEnhancer(),
                deviceTokenEnhancer(),
                jwtAccessTokenConverter()
        ));
        endpoints.authenticationManager(authenticationManager)
                .pathMapping("/oauth/token", "/token")
                .pathMapping("/oauth/authorize", "/authorize")
                .pathMapping("/oauth/token_key", "/token_key")
                .pathMapping("/oauth/check_token", "/check_token")
                .pathMapping("/oauth/confirm_access", "/confirm_access")
                .userDetailsService(userDetailService)
                .tokenStore(redisTokenStore())
                .tokenEnhancer(tokenEnhancerChain)
                .tokenGranter(new CompositeTokenGranter(tokenGranters))
                .exceptionTranslator(new CustomWebResponseExceptionTranslator());
        //CustomResourceOwnerPasswordTokenGranter 替换了默认的 password 授权模式，因为：
        //它使用了相同的 grant_type = "password"
        //在 AuthorizationServerConfig 的 configure 方法中，先添加了自定义的 granters，然后才添加默认的 granter
        //由于 CompositeTokenGranter 在处理授权请求时会使用第一个匹配的 granter，所以自定义的实现会覆盖默认的实现
    }
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        clients.withClientDetails(jdbcClientDetails());
    }

    @Bean
    public ClientDetailsService jdbcClientDetails() {
        return clientDetailService;
    }

    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter jwtAccessTokenConverter = new JwtAccessTokenConverter();
        jwtAccessTokenConverter.setSigningKey(jwtSecretKey);  // 设置签名密钥
        log.info("jwtSecretKey {}", jwtSecretKey);
        return jwtAccessTokenConverter;
    }

    @Bean
    public DeviceTokenEnhancer deviceTokenEnhancer() {
        return new DeviceTokenEnhancer();
    }

    @Bean
    public TokenEnhancer tokenEnhancer() {
        return (accessToken, authentication) -> {
            final Map<String, Object> additionalInfo = new HashMap<>();
            if (authentication.getUserAuthentication() != null) {
                UserExpandDto userExpandDto = (UserExpandDto) authentication.getUserAuthentication().getDetails();
                if (userExpandDto != null) {
                    additionalInfo.put("userId", userExpandDto.getId());
                    if (!StringUtils.isEmpty(userExpandDto.getNeedBindMpAppCode())) {
                        additionalInfo.put("needBindMpAppCode", userExpandDto.getNeedBindMpAppCode());
                        additionalInfo.put("bindResult", userExpandDto.getBindResult());
                    }
                    if (CollUtil.isNotEmpty(userExpandDto.getRoleDtoList())) {
                        additionalInfo.put("roles", userExpandDto.getRoleDtoList());
                    }
                }
            }
            ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
            return accessToken;
        };
    }

    private List<TokenGranter> getTokenGranters(AuthorizationServerTokenServices tokenServices, OAuth2RequestFactory requestFactory) {
        return new ArrayList<>(Arrays.asList(
                new CustomResourceOwnerPasswordTokenGranter(tokenServices, userAuthenticationCommonService, clientDetailService, requestFactory),
                new CustomWechatCodeTokenGranter(userDetailService, userAuthenticationCommonService, clientDetailService, tokenServices, wechatFeign, requestFactory),
                new CustomRegisterTokenGranter(tokenServices, userAuthenticationCommonService, clientDetailService, accountFeign, requestFactory),
                new CustomUserIdSecretTokenGranter(tokenServices, userAuthenticationCommonService, clientDetailService, accountFeign, authRedisUtil, requestFactory),
                new BankJiTokenGranter(tokenServices, userAuthenticationCommonService, clientDetailService, accountFeign, requestFactory),
                new FeishuCodeTokenGranter(tokenServices, userAuthenticationCommonService, clientDetailService, feishuFeign, requestFactory),
                new VerificationCodeTokenGranter(tokenServices, clientDetailService, requestFactory, verificationCodeService, userAuthenticationCommonService, environment)
        ));
    }
}
