package com.jysd.ray.oauth.feign;

import com.jysd.ray.oauth.dto.WechatAccountUserRelationDto;
import com.jysd.ray.oauth.dto.WechatMaLoginDto;
import com.jysd.ray.oauth.dto.WechatUserPrivateInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "RAY-WECHAT-API")
public interface WechatFeign {
    @PostMapping("/mp-users/bind-account-user")
    void bindAccountUser(@RequestBody WechatAccountUserRelationDto bindUserParam, @RequestHeader(name = "r-wechat-application-code") String wxAppCode);

    @PostMapping("/mp-users/unbind-account-user")
    void unbindAccountUser(@RequestBody WechatAccountUserRelationDto bindUserParam, @RequestHeader(name = "r-wechat-application-code") String wxAppCode);

    @PostMapping(value = "/oauth/login/{code}",headers = {"content-type=application/json"})
    WechatMaLoginDto login(@PathVariable(value = "code") String code, @RequestHeader(name = "r-wechat-application-code") String wxAppCode);

    /**
     * 根据用户ID，获取accountId
     */
    @GetMapping("/user-wechat-user-relation/by-user-id")
    WechatUserPrivateInfoDTO getWechatUserByUserId(@RequestParam("userId") Long userId,
                                                   @RequestParam(required = false, value = "appId") Long appId,
                                                   @RequestParam(required = false, value = "appCode") String appCode);


}
