package com.jysd.ray.oauth.service;

import com.jysd.ray.oauth.lib.entity.RayClientDetail;
import com.jysd.ray.oauth.lib.entity.RayUser;
import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;
import com.jysd.ray.oauth.param.BindBankJiParam;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

public interface UserAuthenticationCommonService {

    void bindAccountUser2Wx(Long userId, String openid, String wxAppCode);

    void cacheResourceByUser(UserIdentityEnum identityEnum, RayUser user);

    void removeAccessToken(String clientId, String principal);

    Boolean userBindWxAppRelationResult(Long userId, String wxAppCode);

    BindBankJiParam decryptBankJiExtendParam(String extendParam);

    void bindBankJiUser(String extendParam, Long userId);

    void checkWxAppConfig(RayClientDetail rayClientDetail);

    UsernamePasswordAuthenticationToken authenticateUserInfo(Authentication authentication);
}
