package com.jysd.ray.oauth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatMaLoginDto {
    private String openId;
    private Long userId;

    @Override
    public String toString() {
        return "{" +
                "openId='" + openId + '\'' +
                ", userId=" + userId +
                '}';
    }
}
