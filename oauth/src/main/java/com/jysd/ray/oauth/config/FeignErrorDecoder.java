package com.jysd.ray.oauth.config;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.lib.exception.vo.ErrorVo;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class FeignErrorDecoder implements ErrorDecoder {
    @Override
    public Exception decode(String methodKey, Response response) {
        log.debug("feign exception: methodKey: {}, response.status: {}", methodKey, response.status());
        String bodyMessage = null;
        try {
            bodyMessage = Util.toString(response.body().asReader(Util.UTF_8));
            log.debug("body message:{}", bodyMessage);
            if (!JSONUtil.isJson(bodyMessage)) {
                return new RestException(HttpStatus.resolve(response.status()), "error.feign.unknown", bodyMessage);
            }
            JSONObject msObject = JSONUtil.parseObj(bodyMessage);
            String code = msObject.getStr("code");

            if (!StringUtils.isEmpty(code) && msObject.containsKey("message") &&
                    msObject.containsKey("timestamp")) {
                //自定义已知的异常
                ErrorVo errorVo = JSONUtil.toBean(bodyMessage, ErrorVo.class);
                return new RestException(HttpStatus.resolve(response.status()), errorVo);
            } else {
                //代码运行过程中未知的异常使用
                return new RestException(HttpStatus.resolve(response.status()), "error.feign.unknown", bodyMessage);
            }
        } catch (Exception e) {
            return new RestException(HttpStatus.resolve(response.status()), "error.feign.unknown", bodyMessage);
        }
    }
}
