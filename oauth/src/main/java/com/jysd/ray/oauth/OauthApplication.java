package com.jysd.ray.oauth;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = {"com.jysd.ray.lib.exception", "com.jysd.ray", "com.joinus.ray", "com.jysd.ray.oauth.service"})
@EnableApolloConfig
@EnableEurekaClient
@EnableFeignClients
@MapperScan("com.jysd.ray.oauth.mapper")
public class OauthApplication {

    public static void main(String[] args) {
        SpringApplication.run(OauthApplication.class, args);
    }

}
