package com.jysd.ray.oauth.exception;

import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.lib.exception.util.MessageSourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.util.StringUtils;

@SuppressWarnings("ALL")
@Slf4j
public class CustomWebResponseExceptionTranslator implements WebResponseExceptionTranslator {
    @Override
    public ResponseEntity translate(Exception exception) throws Exception {
        log.error("oauth exception: {}", exception.getMessage());
        if (exception instanceof OAuth2Exception) {
            OAuth2Exception oAuth2Exception = (OAuth2Exception) exception;
            log.error("oAuth2Exception " + oAuth2Exception.getOAuth2ErrorCode() + ": {}", oAuth2Exception.getMessage());
            return ResponseEntity
                    .status(oAuth2Exception.getHttpErrorCode())
                    .body(MessageSourceUtil.getErrorVo("error.oauth.exception", oAuth2Exception.getMessage()));
        } else if (exception instanceof AuthenticationException) {
            AuthenticationException authenticationException = (AuthenticationException) exception;
            log.error("AuthenticationException: {}", authenticationException.getMessage());
            return ResponseEntity
                    .status(HttpStatus.UNAUTHORIZED)
                    .body(MessageSourceUtil.getErrorVo("error.oauth.login-fail", authenticationException.getMessage()));
        } else if (exception instanceof RestException) {
            RestException restException = (RestException) exception;
            //针对于feign错误解码器中抛出的RestException(HttpStatus status, ErrorVo errorVo)情况处理
            if (StringUtils.isEmpty(restException.getMessageSourceCode())) {
                return ResponseEntity.status(restException.getHttpStatus()).body(restException.getErrorVo());
            }
            return ResponseEntity.status(restException.getHttpStatus()).body(MessageSourceUtil.getErrorVo(restException.getMessageSourceCode(), restException.getExtraMessage()));
        }
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(MessageSourceUtil.getErrorVo("error.oauth.exception", exception.getMessage()));
    }
}
