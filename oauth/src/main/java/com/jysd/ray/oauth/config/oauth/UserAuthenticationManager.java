package com.jysd.ray.oauth.config.oauth;

import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.exception.AccountStatusException;
import com.jysd.ray.oauth.lib.dto.RoleDto;
import com.jysd.ray.oauth.lib.dto.UserExpandDto;
import com.jysd.ray.oauth.lib.entity.RayUser;
import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;
import com.jysd.ray.oauth.service.ISchoolService;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import com.jysd.ray.oauth.service.impl.UserDetailServiceImpl;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor
@Component
public class UserAuthenticationManager implements AuthenticationManager {

    @Autowired
    private UserDetailServiceImpl userDetailsService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserAuthenticationCommonService userAuthenticationCommonService;
    @Resource
    private ISchoolService schoolService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if (authentication == null) {
            throw new InvalidTokenException("Invalid token (token not found)");
        }

        PrincipleDTO principleDTO = (PrincipleDTO) authentication.getPrincipal();
        String password = (String) authentication.getCredentials();
        HashMap detailParam = (HashMap) authentication.getDetails();

        RayUser user = getRayUser(principleDTO);

        //校验账号是否正常
        checkAccount(user, password);

        String clientId = (String) detailParam.get("clientId");
        List<RoleDto>  roleDtoList = userDetailsService.getRoles(user.getId(), clientId);
        user.setRoleDtoList(roleDtoList);

        //校验用户身份
        String identityStr = (String) detailParam.get("identity");
        checkIdentity(identityStr, user);

        // 持久化当前用户的resource权限
        userAuthenticationCommonService.cacheResourceByUser(UserIdentityEnum.valueOf(identityStr.toUpperCase()), user);

        //拼装用户认证信息
        UsernamePasswordAuthenticationToken auth = buildAuthenticationToken(user, detailParam);

        // 成功登录后，先删除持久化token，使得产生新的token
        userAuthenticationCommonService.removeAccessToken(clientId, (String) auth.getPrincipal());
        return auth;
    }

    private void checkIdentity(String identityStr, RayUser user) {
        //校验用户登陆身份是否合法
        UserIdentityEnum currentIdentity = checkIdentityStr(identityStr);
        user.setCurrentIdentity(currentIdentity);

        checkAndHandleByUserIdentity(currentIdentity, user);
    }

    private UsernamePasswordAuthenticationToken buildAuthenticationToken(RayUser user, HashMap detailParam) {
        UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(user.getMobile(),
                detailParam.get("password"), null);
        UserExpandDto userExpandDto = new UserExpandDto();
        BeanUtils.copyProperties(user, userExpandDto);
        String needBindMpAppCode = (String) detailParam.get("needBindMpAppCode");
        if (!StringUtils.isEmpty(needBindMpAppCode)) {
            userExpandDto.setNeedBindMpAppCode(needBindMpAppCode);
            userExpandDto.setBindResult(userAuthenticationCommonService.userBindWxAppRelationResult(user.getId(), needBindMpAppCode));
        }
        auth.setDetails(userExpandDto);
        //抹除密码
        auth.eraseCredentials();
        return auth;
    }

    private void checkAndHandleByUserIdentity(UserIdentityEnum currentIdentity, RayUser user) {
        switch (currentIdentity) {
            case SCHOOL:
                checkHadCurrentIdentity(user);
                if (user.getDefaultTeacherSchoolId() == null) {
                    throw new AccountStatusException("无默认学校，无法登录！");
                }
                schoolService.setIsAdminSchoolIds(user);
                break;
            case PARENT:
                if (!user.hasIdentity()) {
                    userDetailsService.setUserIdentityCache(user);
                }
                break;
            default:
                checkHadCurrentIdentity(user);
                break;
        }
    }

    private void checkAccount(RayUser user, String password) {
        if (user == null) {
            log.error("用户账号不存在");
            throw new RestException("error.oauth.user.not-found", "用户不存在");
        }

        if (!user.isAccountNonExpired() || !user.isAccountNonLocked()) {
            throw new AccountStatusException("账号异常，无法登录！");
        }

        //校验密码
        if (!StringUtils.isEmpty(password)) {
            checkPassword(user.getPassword(), password);
        }
        //TODO 校验用户本身属性不合乎正常业务的情况，有待进一步完善
    }

    private void checkPassword(String passwordFromDb, String passwordFromParam) {
        if (StringUtils.isEmpty(passwordFromDb)) {
            throw new RestException("error.account.need-init-pwd");
        }
        if (!passwordEncoder.matches(passwordFromParam, passwordFromDb)) {
            log.error("用户密码输入错误");
            throw new BadCredentialsException("用户名或密码错误！");
        }
    }

    private UserIdentityEnum checkIdentityStr(String identityStr) {
        if (StringUtils.isEmpty(identityStr)) {
            throw OAuth2Exception.create(OAuth2Exception.INVALID_REQUEST, "用户登陆身份不能为空");
        }

        UserIdentityEnum identityEnum;
        try {
            identityEnum = UserIdentityEnum.valueOf(identityStr.toUpperCase());
        } catch (Exception e) {
            throw OAuth2Exception.create(OAuth2Exception.INVALID_REQUEST, "identity不合法");
        }
        return identityEnum;
    }


    private RayUser getRayUser(PrincipleDTO principleDTO) {
        RayUser user = null;
        switch (principleDTO.getPrincipleType()) {
            case USERNAME:
                user = userDetailsService.loadUserByUsername((String) principleDTO.getPrinciple());
                break;
            case MOBILE:
                user = userDetailsService.loadUserByMobile((String) principleDTO.getPrinciple());
                break;
            case USER_ID:
                user = userDetailsService.getById((Long) principleDTO.getPrinciple());
                break;
            default:
        }
        return user;
    }

    private void checkHadCurrentIdentity(RayUser user) {
        if (!user.hasIdentity()) {
            throw new AccountStatusException("您无权登录此服务！");
        }
    }

}
