package com.jysd.ray.oauth.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Notes
 * @date 2024/9/25 8:43
 */
@Data
public class FeishuUserInfo {
    private String name;
    /**
     * 用户英文名称
     * <p> 示例值：
     */
    private String enName;
    /**
     * 用户头像
     * <p> 示例值：
     */
    private String avatarUrl;
    /**
     * 用户头像 72x72
     * <p> 示例值：
     */
    private String avatarThumb;
    /**
     * 用户头像 240x240
     * <p> 示例值：
     */
    private String avatarMiddle;
    /**
     * 用户头像 640x640
     * <p> 示例值：
     */
    private String avatarBig;
    /**
     * 用户在应用内的唯一标识
     * <p> 示例值：
     */
    private String openId;
    /**
     * 用户统一ID
     * <p> 示例值：
     */
    private String unionId;
    /**
     * 用户邮箱
     * <p> 示例值：
     */
    private String email;
    /**
     * 企业邮箱，请先确保已在管理后台启用飞书邮箱服务
     * <p> 示例值：
     */
    private String enterpriseEmail;
    /**
     * 用户 user_id
     * <p> 示例值：
     */
    private String userId;
    /**
     * 用户手机号
     * <p> 示例值：
     */
    private String mobile;
    /**
     * 当前企业标识
     * <p> 示例值：
     */
    private String tenantKey;
    /**
     * 用户工号
     * <p> 示例值：
     */
    private String employeeNo;
}
