package com.jysd.ray.oauth.feign;

import com.jysd.ray.oauth.dto.FeishuUserInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "feishu-adapter", url = "${feishu.adapter.url:}")
public interface FeishuFeign {
    @PostMapping("/api/authorize/code/user_info")
    FeishuUserInfo getUserInfoByCode(@RequestParam("code") String code);
}
