package com.jysd.ray.oauth.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.config.BankJiAESConfig;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.dto.WechatAccountUserRelationDto;
import com.jysd.ray.oauth.dto.WechatUserPrivateInfoDTO;
import com.jysd.ray.oauth.feign.AccountFeign;
import com.jysd.ray.oauth.feign.SysFeign;
import com.jysd.ray.oauth.feign.WechatFeign;
import com.jysd.ray.oauth.lib.entity.RayClientDetail;
import com.jysd.ray.oauth.lib.entity.RayUser;
import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;
import com.jysd.ray.oauth.param.BindBankJiParam;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AccountStatusException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Objects;

@Slf4j
@Service
public class UserAuthenticationCommonServiceImpl implements UserAuthenticationCommonService {

    @Resource
    private WechatFeign wechatFeign;

    @Resource
    private SysFeign sysFeign;

    @Resource
    private AccountFeign accountFeign;

    @Resource
    private RedisTokenStore tokenStore;

    @Resource
    private AuthenticationManager authenticationManager;

    @Resource
    private BankJiAESConfig bankJiAESConfig;

    @Override
    public void bindAccountUser2Wx(Long userId, String openid, String wxAppCode) {

        // 如果有微信用户参数，就把当前用户和微信用户绑定
        log.info("开始绑定微信，用户id为: {}, r-wechat-application-code{}, openid为{} ", userId, wxAppCode, openid);
        if (!StringUtils.isEmpty(openid) && !StringUtils.isEmpty(wxAppCode)) {
            try {
                WechatAccountUserRelationDto wechatAccountUserRelationDto = WechatAccountUserRelationDto.builder().userId(userId).openid(openid).build();
                wechatFeign.bindAccountUser(wechatAccountUserRelationDto, wxAppCode);
            } catch (Exception e) {
                log.error("绑定微信失败： {} 用户id为: {}, r-wechat-application-code{}, openid为{}", e.getLocalizedMessage(), userId, wxAppCode, openid);
                throw e;
            }
        }
    }

    @Override
    public void cacheResourceByUser(UserIdentityEnum identityEnum, RayUser user) {
        try {
            log.info("开始缓存当前用户资源，用户为: {}", user);
            if (UserIdentityEnum.SCHOOL.equals(identityEnum)) {
                sysFeign.cacheResourceByUser(user.getId(), UserIdentityEnum.SCHOOL.name(), user.getDefaultTeacherSchoolId());
            } else {
                sysFeign.cacheResourceByUser(user.getId(), user.getCurrentIdentity().name(), null);
            }
        } catch (Exception e) {
            log.error("缓存用户资源失败： {}", e.getLocalizedMessage());
        }
    }

    @Override
    public void removeAccessToken(String clientId, String principal) {
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName(clientId, principal);
        log.debug("existed accessTokens: {}", accessTokens);
        accessTokens.forEach(o -> {
            OAuth2RefreshToken refreshToken = o.getRefreshToken();
            tokenStore.removeAccessToken(o);
            if (refreshToken != null) {
                tokenStore.removeRefreshToken(refreshToken);
            }
        });
    }

    @Override
    public void removeAccessTokenByDevice(String clientId, String principal, String deviceId) {
        if (StringUtils.isEmpty(deviceId)) {
            // 如果没有设备ID，则清除所有token（保持原有行为）
            removeAccessToken(clientId, principal);
            return;
        }

        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName(clientId, principal);
        log.debug("existed accessTokens for device {}: {}", deviceId, accessTokens);

        // 只清除相同设备ID的token
        accessTokens.forEach(token -> {
            try {
                OAuth2Authentication auth = tokenStore.readAuthentication(token);
                if (auth != null && auth.getOAuth2Request() != null) {
                    Map<String, String> requestParams = auth.getOAuth2Request().getRequestParameters();
                    String tokenDeviceId = requestParams.get("deviceId");

                    // 如果token的设备ID与当前设备ID相同，则清除
                    if (deviceId.equals(tokenDeviceId)) {
                        OAuth2RefreshToken refreshToken = token.getRefreshToken();
                        tokenStore.removeAccessToken(token);
                        if (refreshToken != null) {
                            tokenStore.removeRefreshToken(refreshToken);
                        }
                        log.info("Removed token for device: {}", deviceId);
                    }
                }
            } catch (Exception e) {
                log.warn("Error processing token for device cleanup: {}", e.getMessage());
            }
        });
    }

    @Override
    public Boolean userBindWxAppRelationResult(Long userId, String wxAppCode) {
        WechatUserPrivateInfoDTO wechatUserPrivateInfoDTO;
        try {
            wechatUserPrivateInfoDTO = wechatFeign.getWechatUserByUserId(userId, null, wxAppCode);
        } catch (Exception e) {
            log.error("获取用户与微信应用关系失败 error cause:{}", e.getMessage());
            throw new RestException("error.feign.unknown");
        }
        if (Objects.isNull(wechatUserPrivateInfoDTO)) {
            return false;
        } else {
            return !StringUtils.isEmpty(wechatUserPrivateInfoDTO.getOpenid());
        }
    }

    @Override
    public BindBankJiParam decryptBankJiExtendParam(String extendParam) {
        try {
            log.debug("extendparam:{}", extendParam);
            //base64解码
            byte[] encryptBody = Base64.decode(extendParam);
            //aes解密
            String jsonStr = new AES(Mode.CBC, Padding.PKCS5Padding, bankJiAESConfig.getKey().getBytes(), bankJiAESConfig.getIv().getBytes()).decryptStr(encryptBody);
            JSONObject json = JSONUtil.parseObj(jsonStr);
            BindBankJiParam bindBankJiParam = BindBankJiParam.builder()
                    .bankJiUserId(json.getStr("custCoreNo"))
                    .mobile(json.getStr("custMobile"))
                    .build();
            return bindBankJiParam;
        } catch (Exception e) {
            throw new RestException(HttpStatus.BAD_REQUEST, "error.basic.param-invalid", "extendparam解密失败");
        }

    }

    @Override
    public void bindBankJiUser(String extendParam, Long userId) {
        BindBankJiParam bindBankJiParam = this.decryptBankJiExtendParam(extendParam);
        bindBankJiParam.setUserId(userId);
        accountFeign.bindBankJiUser(bindBankJiParam);
    }

    @Override
    public void checkWxAppConfig(RayClientDetail rayClientDetail) {
        String wxAppCode = rayClientDetail.getWechatApplicationCode();
        if (StringUtils.isEmpty(wxAppCode)) {
            log.error("应用未配置微信信息,应用id为:{}", rayClientDetail.getClientId());
            throw new RestException("error.sys.not-set-wx-config", "该应用未配置微信信息");
        }
    }

    @Override
    public UsernamePasswordAuthenticationToken authenticateUserInfo(Authentication userAuth) {
        UsernamePasswordAuthenticationToken newAuth;
        try {
            newAuth = (UsernamePasswordAuthenticationToken) authenticationManager.authenticate(userAuth);
        } catch (AccountStatusException ase) {
            log.error("AccountStatusException: {}", ase.getMessage());
            //covers expired, locked, disabled cases (mentioned in section 5.2, draft 31)
            throw new InvalidGrantException(ase.getMessage());
        } catch (BadCredentialsException e) {
            log.error("BadCredentialsException: {}", e.getMessage());
            // If the username/password are wrong the spec says we should send 400/invalid grant
            throw new InvalidGrantException(e.getMessage());
        }
        if (newAuth == null || !newAuth.isAuthenticated()) {
            PrincipleDTO principalDTO = (PrincipleDTO) userAuth.getPrincipal();
            log.error("认证用户失败, 登陆账号信息:{}", principalDTO);
            throw new InvalidGrantException("Could not authenticate user");
        }
        return newAuth;
    }

}
