package com.jysd.ray.oauth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.ray.lib.redis.util.AuthRedisUtil;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.constant.GrantTypeContanst;
import com.jysd.ray.oauth.dto.UserSecretDto;
import com.jysd.ray.oauth.feign.SysFeign;
import com.jysd.ray.oauth.lib.dto.RoleDto;
import com.jysd.ray.oauth.lib.entity.RayUser;
import com.jysd.ray.oauth.mapper.UserMapper;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
public class UserDetailServiceImpl extends ServiceImpl<UserMapper, RayUser> implements UserDetailsService {

    @Resource
    private AuthRedisUtil authRedisUtil;

    @Resource
    private SysFeign sysFeign;

    public RayUser loadUserByMobile(String mobile) {
        return lambdaQuery().eq(RayUser::getMobile, mobile).isNull(RayUser::getDeletedAt).last("limit 1").one();
    }

    @Override
    public RayUser loadUserByUsername(String username) throws UsernameNotFoundException {
        return lambdaQuery().eq(RayUser::getUsername, username).isNull(RayUser::getDeletedAt).last("limit 1").one();
    }

    public void setUserIdentityCache(RayUser user) {
        //存储设置用户身份时需要的缓存信息
        String secret = UUID.randomUUID().toString();
        UserSecretDto userSecretDto = new UserSecretDto();
        userSecretDto.setUserId(user.getId());
        userSecretDto.setSecret(secret);
        authRedisUtil.setEx(GrantTypeContanst.getCacheUserIdSecret(user.getId()), secret, 5, TimeUnit.MINUTES);
        throw new RestException("error.oauth.not-have-parent-identity", userSecretDto.toString());
    }

    public List<RoleDto> getRoles(Long id, String clientId) {
        return sysFeign.listRoleByUserIdAndClientId(id, clientId);
    }
}
