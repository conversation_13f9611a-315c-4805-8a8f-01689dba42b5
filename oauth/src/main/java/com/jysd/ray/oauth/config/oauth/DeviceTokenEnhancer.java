package com.jysd.ray.oauth.config.oauth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备信息Token增强器
 * 用于在token中添加设备相关信息，支持多设备登录
 */
@Slf4j
public class DeviceTokenEnhancer implements TokenEnhancer {

    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        if (authentication.getOAuth2Request() != null) {
            Map<String, String> requestParameters = authentication.getOAuth2Request().getRequestParameters();
            String deviceId = requestParameters.get("deviceId");
            String allowMultipleDevices = requestParameters.get("allowMultipleDevices");
            
            if (!StringUtils.isEmpty(deviceId) || !StringUtils.isEmpty(allowMultipleDevices)) {
                Map<String, Object> additionalInfo = new HashMap<>();
                
                if (!StringUtils.isEmpty(deviceId)) {
                    additionalInfo.put("deviceId", deviceId);
                    log.debug("Adding deviceId to token: {}", deviceId);
                }
                
                if (!StringUtils.isEmpty(allowMultipleDevices)) {
                    additionalInfo.put("allowMultipleDevices", Boolean.parseBoolean(allowMultipleDevices));
                    log.debug("Adding allowMultipleDevices to token: {}", allowMultipleDevices);
                }
                
                // 合并现有的附加信息
                if (accessToken.getAdditionalInformation() != null) {
                    additionalInfo.putAll(accessToken.getAdditionalInformation());
                }
                
                ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
            }
        }
        
        return accessToken;
    }
}
