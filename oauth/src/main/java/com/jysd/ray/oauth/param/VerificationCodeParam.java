package com.jysd.ray.oauth.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationCodeParam implements Serializable {

    @NotEmpty(message = "手机号码不能为空")
    private String mobile;

    @NotEmpty(message = "sessionId不能为空")
    private String sessionId;

    @NotEmpty(message = "sig不能为空")
    private String sig;

    @NotEmpty(message = "token不能为空")
    private String token;

    @NotEmpty(message = "scene不能为空")
    private String scene;

}
