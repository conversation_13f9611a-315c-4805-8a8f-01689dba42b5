package com.jysd.ray.oauth.service.impl;

import com.jysd.ray.oauth.dto.DeviceInfoDto;
import com.jysd.ray.oauth.service.DeviceManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2RefreshToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备管理服务实现
 */
@Slf4j
@Service
public class DeviceManagementServiceImpl implements DeviceManagementService {

    @Resource
    private RedisTokenStore tokenStore;

    @Override
    public List<DeviceInfoDto> getUserDevices(String clientId, String username) {
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName(clientId, username);
        
        List<DeviceInfoDto> devices = new ArrayList<>();
        
        for (OAuth2AccessToken token : accessTokens) {
            try {
                OAuth2Authentication auth = tokenStore.readAuthentication(token);
                if (auth != null && auth.getOAuth2Request() != null) {
                    Map<String, String> requestParams = auth.getOAuth2Request().getRequestParameters();
                    String deviceId = requestParams.get("deviceId");
                    
                    // 如果没有设备ID，跳过（兼容旧版本）
                    if (StringUtils.isEmpty(deviceId)) {
                        continue;
                    }
                    
                    DeviceInfoDto deviceInfo = buildDeviceInfo(token, auth, deviceId, requestParams);
                    devices.add(deviceInfo);
                }
            } catch (Exception e) {
                log.warn("Error processing token for device list: {}", e.getMessage());
            }
        }
        
        // 按登录时间倒序排列
        return devices.stream()
                .sorted((d1, d2) -> d2.getLoginTime().compareTo(d1.getLoginTime()))
                .collect(Collectors.toList());
    }

    @Override
    public void removeDevice(String clientId, String username, String deviceId) {
        if (StringUtils.isEmpty(deviceId)) {
            log.warn("Device ID is empty, cannot remove device");
            return;
        }
        
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName(clientId, username);
        
        for (OAuth2AccessToken token : accessTokens) {
            try {
                OAuth2Authentication auth = tokenStore.readAuthentication(token);
                if (auth != null && auth.getOAuth2Request() != null) {
                    Map<String, String> requestParams = auth.getOAuth2Request().getRequestParameters();
                    String tokenDeviceId = requestParams.get("deviceId");
                    
                    if (deviceId.equals(tokenDeviceId)) {
                        OAuth2RefreshToken refreshToken = token.getRefreshToken();
                        tokenStore.removeAccessToken(token);
                        if (refreshToken != null) {
                            tokenStore.removeRefreshToken(refreshToken);
                        }
                        log.info("Removed token for device: {} of user: {}", deviceId, username);
                        break;
                    }
                }
            } catch (Exception e) {
                log.warn("Error removing device token: {}", e.getMessage());
            }
        }
    }

    @Override
    public void removeOtherDevices(String clientId, String username, String keepDeviceId) {
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName(clientId, username);
        
        for (OAuth2AccessToken token : accessTokens) {
            try {
                OAuth2Authentication auth = tokenStore.readAuthentication(token);
                if (auth != null && auth.getOAuth2Request() != null) {
                    Map<String, String> requestParams = auth.getOAuth2Request().getRequestParameters();
                    String tokenDeviceId = requestParams.get("deviceId");
                    
                    // 如果不是要保留的设备，则删除
                    if (!Objects.equals(keepDeviceId, tokenDeviceId)) {
                        OAuth2RefreshToken refreshToken = token.getRefreshToken();
                        tokenStore.removeAccessToken(token);
                        if (refreshToken != null) {
                            tokenStore.removeRefreshToken(refreshToken);
                        }
                        log.info("Removed token for device: {} of user: {}", tokenDeviceId, username);
                    }
                }
            } catch (Exception e) {
                log.warn("Error removing other device tokens: {}", e.getMessage());
            }
        }
    }

    @Override
    public void removeAllDevices(String clientId, String username) {
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName(clientId, username);
        
        for (OAuth2AccessToken token : accessTokens) {
            try {
                OAuth2RefreshToken refreshToken = token.getRefreshToken();
                tokenStore.removeAccessToken(token);
                if (refreshToken != null) {
                    tokenStore.removeRefreshToken(refreshToken);
                }
            } catch (Exception e) {
                log.warn("Error removing all device tokens: {}", e.getMessage());
            }
        }
        
        log.info("Removed all tokens for user: {}", username);
    }

    /**
     * 构建设备信息
     */
    private DeviceInfoDto buildDeviceInfo(OAuth2AccessToken token, OAuth2Authentication auth, 
                                         String deviceId, Map<String, String> requestParams) {
        
        // 从token的附加信息中获取设备相关信息
        Map<String, Object> additionalInfo = token.getAdditionalInformation();
        
        return DeviceInfoDto.builder()
                .deviceId(deviceId)
                .deviceType(extractDeviceType(deviceId))
                .osInfo(requestParams.get("osInfo"))
                .browserInfo(requestParams.get("browserInfo"))
                .ipAddress(requestParams.get("ipAddress"))
                .loginTime(extractLoginTime(additionalInfo))
                .lastActiveTime(LocalDateTime.now()) // 可以从Redis中获取更准确的时间
                .expireTime(extractExpireTime(token))
                .isCurrent(false) // 需要额外逻辑判断
                .tokenPreview(generateTokenPreview(token.getValue()))
                .build();
    }

    /**
     * 从设备ID中提取设备类型
     */
    private String extractDeviceType(String deviceId) {
        if (StringUtils.isEmpty(deviceId)) {
            return "Unknown";
        }
        
        if (deviceId.startsWith("PC_")) {
            return "PC";
        } else if (deviceId.startsWith("Mobile_")) {
            return "Mobile";
        } else if (deviceId.startsWith("Tablet_")) {
            return "Tablet";
        } else {
            return "Unknown";
        }
    }

    /**
     * 提取登录时间
     */
    private LocalDateTime extractLoginTime(Map<String, Object> additionalInfo) {
        if (additionalInfo != null && additionalInfo.containsKey("iat")) {
            // JWT中的iat字段表示签发时间
            Long iat = (Long) additionalInfo.get("iat");
            return LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochSecond(iat), 
                    ZoneId.systemDefault()
            );
        }
        return LocalDateTime.now();
    }

    /**
     * 提取过期时间
     */
    private LocalDateTime extractExpireTime(OAuth2AccessToken token) {
        if (token.getExpiration() != null) {
            return LocalDateTime.ofInstant(
                    token.getExpiration().toInstant(), 
                    ZoneId.systemDefault()
            );
        }
        return null;
    }

    /**
     * 生成Token预览（只显示前几位和后几位）
     */
    private String generateTokenPreview(String tokenValue) {
        if (StringUtils.isEmpty(tokenValue) || tokenValue.length() < 10) {
            return "****";
        }
        
        String prefix = tokenValue.substring(0, 4);
        String suffix = tokenValue.substring(tokenValue.length() - 4);
        return prefix + "****" + suffix;
    }
}
