package com.jysd.ray.oauth.config.oauth;

import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.dto.UserDto;
import com.jysd.ray.oauth.enums.PrincipleTypeEnum;
import com.jysd.ray.oauth.feign.AccountFeign;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.util.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 针对河北银行app嵌入H5页面的登录方式
 * 河北银行app跳转H5页面携带加密用户参数
 * https://xxx.xxxx.xxx/xxx?extendparam=BASE64(AES加密密文)。AES加密原文={"custCoreNo":"张三","custMobile": "138XXXXXXXX"}，即属性为custCoreNo(核心客户号)、custMobile（客户手机号）的json对象。
 * 解析参数，关联系统用户与河北银行账户
 * 返回token
 * @date 2021/7/30
 */
@Slf4j
public class BankJiTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "bank_ji";

    private AccountFeign accountFeign;

    private UserAuthenticationCommonService userAuthenticationCommonService;

    public BankJiTokenGranter(AuthorizationServerTokenServices tokenServices,
                              UserAuthenticationCommonService userAuthenticationCommonService,
                              ClientDetailsService clientDetailsService,
                              AccountFeign accountFeign,
                              OAuth2RequestFactory requestFactory) {
        this(userAuthenticationCommonService, tokenServices, clientDetailsService, accountFeign, requestFactory, GRANT_TYPE);
    }

    protected BankJiTokenGranter(UserAuthenticationCommonService userAuthenticationCommonService, AuthorizationServerTokenServices tokenServices, ClientDetailsService clientDetailsService, AccountFeign accountFeign, OAuth2RequestFactory requestFactory, String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.accountFeign = accountFeign;
        this.userAuthenticationCommonService = userAuthenticationCommonService;
    }


    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
        parameters.put("identity", "PARENT");
        String clientId = client.getClientId();
        parameters.put("clientId", clientId);

        //base64的aes加密密文
        String extendparam = parameters.get("extendparam");
        if (StringUtils.isEmpty(extendparam)) {
            throw new RestException(HttpStatus.BAD_REQUEST, "error.basic.param-invalid", "extendparam不能为空");
        }
        //解密加密过的参数信息
        var bindBankJiParam = userAuthenticationCommonService.decryptBankJiExtendParam(extendparam);

        Optional<UserDto> userDtoOpt = accountFeign.loginOrCreate(bindBankJiParam);
        if (userDtoOpt.isEmpty()) {
            throw new RestException("error.account.bank-ji-not-bind", "未查询到用户，请先登录或注册");
        }
        PrincipleDTO principleDTO = PrincipleDTO.builder()
                .principle(userDtoOpt.get().getId())
                .principleType(PrincipleTypeEnum.USER_ID)
                .build();

        PreAuthenticatedAuthenticationToken userAuth = new PreAuthenticatedAuthenticationToken(principleDTO, null);
        log.debug("userAuth: {}", userAuth);

        userAuth.setDetails(parameters);
        UsernamePasswordAuthenticationToken newAuth =
                userAuthenticationCommonService.authenticateUserInfo(userAuth);

        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, newAuth);
    }
}
