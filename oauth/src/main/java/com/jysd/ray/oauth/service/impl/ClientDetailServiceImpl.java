package com.jysd.ray.oauth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.lib.entity.RayClientDetail;
import com.jysd.ray.oauth.mapper.ClientDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class ClientDetailServiceImpl extends ServiceImpl<ClientDetailMapper, RayClientDetail> implements ClientDetailsService {

    @Override
    public RayClientDetail loadClientByClientId(String clientId) throws ClientRegistrationException {
        RayClientDetail clientDetail = baseMapper.selectById(clientId);
        if (Objects.isNull(clientDetail)) {
            log.error("未找到应用,应用id为:{}", clientId);
            throw new RestException("error.sys.not-found-client", "未找到应用！");
        }

        return clientDetail;
    }


}
