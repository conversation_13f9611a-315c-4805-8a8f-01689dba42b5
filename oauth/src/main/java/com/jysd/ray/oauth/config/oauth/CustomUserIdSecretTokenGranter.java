package com.jysd.ray.oauth.config.oauth;

import com.joinus.ray.lib.redis.util.AuthRedisUtil;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.constant.GrantTypeContanst;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.dto.UserDto;
import com.jysd.ray.oauth.dto.UserIdentityDto;
import com.jysd.ray.oauth.enums.DataActionTypeEnum;
import com.jysd.ray.oauth.enums.PrincipleTypeEnum;
import com.jysd.ray.oauth.feign.AccountFeign;
import com.jysd.ray.oauth.lib.entity.RayClientDetail;
import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.util.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class CustomUserIdSecretTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "user_id_secret";

    private AuthRedisUtil authRedisUtil;

    private AccountFeign accountFeign;

    private UserAuthenticationCommonService userAuthenticationCommonService;


    public CustomUserIdSecretTokenGranter(
            AuthorizationServerTokenServices tokenServices,
            UserAuthenticationCommonService userAuthenticationCommonService,
            ClientDetailsService clientDetailsService,
            AccountFeign accountFeign,
            AuthRedisUtil authRedisUtil,
            OAuth2RequestFactory requestFactory) {
        this(tokenServices, userAuthenticationCommonService, clientDetailsService, accountFeign, authRedisUtil, requestFactory, GRANT_TYPE);
    }

    protected CustomUserIdSecretTokenGranter(AuthorizationServerTokenServices tokenServices,
                                             UserAuthenticationCommonService userAuthenticationCommonService,
                                             ClientDetailsService clientDetailsService,
                                             AccountFeign accountFeign,
                                             AuthRedisUtil authRedisUtil,
                                             OAuth2RequestFactory requestFactory,
                                             String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.accountFeign = accountFeign;
        this.authRedisUtil = authRedisUtil;
        this.userAuthenticationCommonService = userAuthenticationCommonService;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
        String clientId = client.getClientId();
        parameters.put("clientId", clientId);

        //区别与授权模式,该参数代表设置身份时对应的OauthRedis缓存中的value属性
        String secret = parameters.get("secret");
        if (StringUtils.isEmpty(secret)) {
            throw new RestException(HttpStatus.BAD_REQUEST, "error.basic.param-invalid", "secret不能为空");
        }
        String userId = parameters.get("userId");
        if (StringUtils.isEmpty(userId)) {
            throw new RestException(HttpStatus.BAD_REQUEST, "error.basic.param-invalid", "userId不能为空");
        }

        String secretFromCache = authRedisUtil.get(GrantTypeContanst.getCacheUserIdSecret(Long.parseLong(userId)));
        if (StringUtils.isEmpty(secretFromCache) || !secretFromCache.equals(secret)) {
            throw new RestException("error.oauth.set-identity-secret-invalid", "secret已失效或匹配错误！");
        }

        RayClientDetail rayClientDetail = (RayClientDetail) client;
        userAuthenticationCommonService.checkWxAppConfig(rayClientDetail);

        //更新用户身份
        UserIdentityDto userIdentityDto = UserIdentityDto.builder()
                .userIdentity(UserIdentityEnum.valueOf(parameters.get("identity")))
                .identityActionType(DataActionTypeEnum.ADD)
                .build();
        //updateDto 返回的是用户全量信息
        UserDto updateDto = accountFeign.update(Long.parseLong(userId), userIdentityDto);
        if (Objects.isNull(updateDto)) {
            throw new RestException("error.account.update-user-fail", "用户更新失败！");
        }

        PrincipleDTO principleDTO = PrincipleDTO.builder()
                .principle(updateDto.getId())
                .principleType(PrincipleTypeEnum.USER_ID)
                .build();

        PreAuthenticatedAuthenticationToken userAuth = new PreAuthenticatedAuthenticationToken(principleDTO, null);
        log.debug("userAuth: {}", userAuth);

        userAuth.setDetails(parameters);
        UsernamePasswordAuthenticationToken newAuth =
                userAuthenticationCommonService.authenticateUserInfo(userAuth);

        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, newAuth);
    }

}
