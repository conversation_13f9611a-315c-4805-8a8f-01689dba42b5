package com.jysd.ray.oauth.config.oauth;

import cn.hutool.core.bean.BeanUtil;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.enums.PrincipleTypeEnum;
import com.jysd.ray.oauth.lib.dto.UserExpandDto;
import com.jysd.ray.oauth.lib.entity.RayClientDetail;
import com.jysd.ray.oauth.param.UserRegistParam;
import com.jysd.ray.oauth.dto.UserRegisterDto;
import com.jysd.ray.oauth.feign.AccountFeign;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class CustomRegisterTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "register";
    private AccountFeign accountFeign;
    private UserAuthenticationCommonService userAuthenticationCommonService;

    public CustomRegisterTokenGranter(
            AuthorizationServerTokenServices tokenServices,
            UserAuthenticationCommonService userAuthenticationCommonService,
            ClientDetailsService clientDetailsService,
            AccountFeign accountFeign,
            OAuth2RequestFactory requestFactory) {
        this(tokenServices, userAuthenticationCommonService, clientDetailsService, accountFeign, requestFactory, GRANT_TYPE);
    }


    protected CustomRegisterTokenGranter(AuthorizationServerTokenServices tokenServices,
                                         UserAuthenticationCommonService userAuthenticationCommonService,
                                         ClientDetailsService clientDetailsService,
                                         AccountFeign accountFeign,
                                         OAuth2RequestFactory requestFactory,
                                         String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.accountFeign = accountFeign;
        this.userAuthenticationCommonService = userAuthenticationCommonService;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {
        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
        String clientId = client.getClientId();
        parameters.put("clientId", clientId);

        //用户注册
        var userRegisterParam = BeanUtil.fillBeanWithMap(new HashMap<String, Object>(parameters), new UserRegistParam(), false);
        UserRegisterDto userRegister = accountFeign.register(userRegisterParam);
        if (null == userRegister) {
            throw new RestException("error.user.register-fail", "用户注册失败！");
        }

        PrincipleDTO principleDTO = PrincipleDTO.builder()
                .principle(userRegister.getId())
                .principleType(PrincipleTypeEnum.USER_ID)
                .build();
        PreAuthenticatedAuthenticationToken userAuth = new PreAuthenticatedAuthenticationToken(principleDTO, null);
        log.debug("userAuth: {}", userAuth);

        userAuth.setDetails(parameters);
        UsernamePasswordAuthenticationToken newAuth =
                userAuthenticationCommonService.authenticateUserInfo(userAuth);

        UserExpandDto userExpandDto = (UserExpandDto) newAuth.getDetails();
        if (parameters.containsKey("openId")) {
            var rayClientDetail = (RayClientDetail) client;
            //校验应用微信配置信息是否完整
            userAuthenticationCommonService.checkWxAppConfig(rayClientDetail);
            //绑定微信用户
            userAuthenticationCommonService.bindAccountUser2Wx(userExpandDto.getId(), parameters.get("openId"),
                    rayClientDetail.getWechatApplicationCode());
        } else if (parameters.containsKey("extendparam")) {
            //绑定用户与河北银行账户的关系
            userAuthenticationCommonService.bindBankJiUser(parameters.get("extendparam"), userExpandDto.getId());
        }

        var storedOauth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOauth2Request, newAuth);

    }

}