package com.jysd.ray.oauth.feign;

import com.jysd.ray.oauth.dto.UserDto;
import com.jysd.ray.oauth.dto.UserIdentityDto;
import com.jysd.ray.oauth.param.BindBankJiParam;
import com.jysd.ray.oauth.param.UserRegistParam;
import com.jysd.ray.oauth.dto.UserRegisterDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Optional;

@FeignClient(value = "RAY-ACCOUNT-API")
public interface AccountFeign {

    @PostMapping("/users/register")
    UserRegisterDto register(@RequestBody UserRegistParam userRegistParam);

    @PutMapping("/users/{id}")
    UserDto update(@PathVariable("id") Long id, @RequestBody UserIdentityDto userIdentityDto);

    @PostMapping("/users/create-parent-by-mobile/{mobile}")
    UserDto createByMobile(@PathVariable("mobile") String mobile);

    @PostMapping("/bank-ji/login-or-create")
    Optional<UserDto> loginOrCreate(@RequestBody BindBankJiParam bindBankJiParam);

    @PostMapping("/bank-ji/bind")
    void bindBankJiUser(@RequestBody BindBankJiParam bindBankJiParam);
    }
