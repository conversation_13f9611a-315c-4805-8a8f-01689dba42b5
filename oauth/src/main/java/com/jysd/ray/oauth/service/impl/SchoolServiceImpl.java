package com.jysd.ray.oauth.service.impl;

import com.jysd.ray.oauth.feign.SchoolFeign;
import com.jysd.ray.oauth.lib.entity.RayUser;
import com.jysd.ray.oauth.service.ISchoolService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 10:46
 * @Version 1.0
 */
@Service
public class SchoolServiceImpl implements ISchoolService {

	@Resource
	private SchoolFeign schoolFeign;

	@Override
	public void setIsAdminSchoolIds(RayUser user) {
		List<Long> adminSchoolIds = schoolFeign.listIsAdminSchoolIdsByUserId(user.getId());
		user.setAdminSchoolIds(adminSchoolIds);
	}
}
