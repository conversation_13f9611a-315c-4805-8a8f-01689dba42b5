package com.jysd.ray.oauth.feign;

import com.jysd.ray.oauth.dto.ClientDto;
import com.jysd.ray.oauth.lib.dto.RoleDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "RAY-SYS-API")
public interface SysFeign {
    @GetMapping("/resources/by-user/{userId}")
    void cacheResourceByUser(@PathVariable("userId") Long userId,
                             @RequestParam("identity") String identity,
                             @RequestParam("identityTargetId") Long identityTargetId);

    /**
     * 查询应用详情
     * @param clientId 应用id
     * @return ClientVo
     */
    @GetMapping("/clients/{clientId}")
    ClientDto getByClientId(@PathVariable("clientId") String clientId);

    @GetMapping("/user-roles/by-user-client")
    List<RoleDto> listRoleByUserIdAndClientId(@RequestParam("userId") Long userId, @RequestParam("clientId") String clientId);

}
