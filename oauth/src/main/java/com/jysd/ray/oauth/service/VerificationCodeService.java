package com.jysd.ray.oauth.service;

import com.jysd.ray.oauth.enums.ClientVerificationCodeEnum;
import com.jysd.ray.oauth.param.VerificationCodeParam;

public interface VerificationCodeService {

    /**
     * [mobile]
     * @return java.lang.Object
     * @description 发送登录验证码
     * <AUTHOR>
     * @date 2024/12/20 09:33
     */
    String sendVerificationCode(VerificationCodeParam param, ClientVerificationCodeEnum clientVerificationCodeEnum);

    /**
     * 验证验证码
     * @param mobile 手机号
     * @param code 验证码
     * @return 是否验证通过
     */
    boolean verifyCode(String mobile, String code, ClientVerificationCodeEnum clientVerificationCodeEnum);

}
