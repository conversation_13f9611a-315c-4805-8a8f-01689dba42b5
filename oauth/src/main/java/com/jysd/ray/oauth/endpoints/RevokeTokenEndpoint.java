package com.jysd.ray.oauth.endpoints;

import com.jysd.ray.oauth.dto.ClientDto;
import com.jysd.ray.oauth.dto.WechatAccountUserRelationDto;
import com.jysd.ray.oauth.feign.SysFeign;
import com.jysd.ray.oauth.feign.WechatFeign;
import com.jysd.ray.oauth.lib.entity.RayUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.endpoint.FrameworkEndpoint;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@FrameworkEndpoint
public class RevokeTokenEndpoint {
    @Resource
    @Qualifier("consumerTokenServices")
    ConsumerTokenServices consumerTokenServices;
    @Resource
    RedisTokenStore redisTokenStore;
    @Resource
    private WechatFeign wechatFeign;
    @Resource
    private SysFeign sysFeign;

    @PostMapping("/revoke-token")
    @ResponseBody
    public void revokeToken() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String accessToken = org.apache.commons.lang.StringUtils.remove(request.getHeader("Authorization"), "Bearer").trim();

        OAuth2Authentication oauth2Authentication = redisTokenStore.readAuthentication(accessToken);
        log.debug("begin revoke token, param: {}, oauth2Authentication: {}", request, oauth2Authentication);
        if (null != oauth2Authentication) {
            //在登录时已经将用户信息存入oauth2Authentication中
            RayUser currentUser = (RayUser) oauth2Authentication.getUserAuthentication().getDetails();
            Long accountUserId = currentUser.getId();

            //去除token信息，再次登陆时重新获得
            consumerTokenServices.revokeToken(accessToken);

            //获取微信应用信息
            String clientId = oauth2Authentication.getOAuth2Request().getClientId();
            ClientDto clientDto = sysFeign.getByClientId(clientId);
            String wechatApplicationCode = clientDto.getWechatApplicationCode();

            if (!StringUtils.isEmpty(wechatApplicationCode)) {
                String openid = request.getParameter("openId");
                WechatAccountUserRelationDto unWechatAccountUserRelationDto = WechatAccountUserRelationDto.builder().userId(accountUserId).openid(openid).build();
                try {
                    //若openid为空，解绑系统用户下所有的微信账号，反之只解绑openid对应的微信账号
                    log.info("开始解绑微信，用户为: {}, r-wechat-application-code{}, openid为{} ", currentUser, wechatApplicationCode, openid);
                    wechatFeign.unbindAccountUser(unWechatAccountUserRelationDto, wechatApplicationCode);
                } catch (Exception e) {
                    log.error("解绑定微信失败： {} 用户为: {}, r-wechat-application-code{}, openid为{}", e.getLocalizedMessage(), currentUser, wechatApplicationCode, openid);
                    throw e;
                }
            }
        }
    }
}
