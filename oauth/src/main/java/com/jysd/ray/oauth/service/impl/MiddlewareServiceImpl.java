package com.jysd.ray.oauth.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jysd.ray.lib.exception.RestException;
import com.jysd.ray.oauth.dto.SmsRequestDTO;
import com.jysd.ray.oauth.service.MiddlewareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MiddlewareServiceImpl implements MiddlewareService {

    @Value("${middleware.sms.domain:https://joinusware.uat.fangxiao.top}")
    private String middlewareSmsDomain;

    private final Integer connectionTimeout = 3 * 1000;
    private final Integer readTimeout = 10 * 000;

    @Override
    public void sendVerificationCode(SmsRequestDTO smsRequestDTO) {
        String logPrefix = StrUtil.format("oauth登录发送验证码 mobile {}", smsRequestDTO.getPhoneNum());
        HttpResponse response = null;
        try {
            log.info("{} 发送验证码 请求参数 {}", logPrefix, JSONUtil.toJsonStr(smsRequestDTO));
            response = this.doPost("/sms/sendYzmSms", smsRequestDTO);
            log.info("{} 发送验证码 返回结果 {}", logPrefix, response.body());
        } catch (Exception e) {
            log.info("{} 发送验证码异常 {}", logPrefix, e.getMessage(), e);
            throw new RestException(HttpStatus.BAD_REQUEST, "error.oauth.exception", "发送验证码异常，请稍后再试");
        }
        if (!response.isOk()) {
            throw new RestException(HttpStatus.BAD_REQUEST, "error.oauth.exception", "发送验证码失败" + response.body());
        }

    }

    @Override
    public boolean verifyCode(SmsRequestDTO smsRequestDTO) {
        String logPrefix = StrUtil.format("oauth登录校验验证码 mobile {}", smsRequestDTO.getPhoneNum());
        HttpResponse response = null;
        try {
            log.info("{} 请求参数 {}", logPrefix, JSONUtil.toJsonStr(smsRequestDTO));
            response = this.doPost("/sms/verifyYzm", smsRequestDTO);
            log.info("{} 返回结果 {}", logPrefix, response.body());
        } catch (Exception e) {
            log.info("{} {}", logPrefix, e.getMessage(), e);
            throw new RestException(HttpStatus.BAD_REQUEST, "error.oauth.exception", "校验验证码异常，请稍后再试");
        }
        if (response.isOk()) {
            JSONObject responseBody = JSONUtil.parseObj(response.body());
            return "true".equals(responseBody.getStr("data"));
        }
        throw new RestException(HttpStatus.BAD_REQUEST, "error.oauth.exception", "校验验证码失败" + response.body());

    }

    private HttpResponse doPost(String path, SmsRequestDTO request) {
        return HttpUtil.createPost(middlewareSmsDomain + path)
                .body(JSONUtil.toJsonStr(request))
                .setConnectionTimeout(connectionTimeout)
                .setReadTimeout(readTimeout)
                .execute();
    }
}
