package com.jysd.ray.oauth.config.oauth;

import com.jysd.ray.oauth.dto.PrincipleDTO;
import com.jysd.ray.oauth.enums.PrincipleTypeEnum;
import com.jysd.ray.oauth.lib.dto.UserExpandDto;
import com.jysd.ray.oauth.lib.entity.RayClientDetail;
import com.jysd.ray.oauth.service.UserAuthenticationCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.*;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.util.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class CustomResourceOwnerPasswordTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "password";

    private UserAuthenticationCommonService userAuthenticationCommonService;

    public CustomResourceOwnerPasswordTokenGranter(AuthorizationServerTokenServices tokenServices,
                                                   UserAuthenticationCommonService userAuthenticationCommonService,
                                                   ClientDetailsService clientDetailsService,
                                                   OAuth2RequestFactory requestFactory) {
        this(tokenServices, userAuthenticationCommonService, clientDetailsService, requestFactory, GRANT_TYPE);
    }

    protected CustomResourceOwnerPasswordTokenGranter(AuthorizationServerTokenServices tokenServices,
                                                      UserAuthenticationCommonService userAuthenticationCommonService, ClientDetailsService clientDetailsService,
                                                      OAuth2RequestFactory requestFactory, String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.userAuthenticationCommonService = userAuthenticationCommonService;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());
        String username = parameters.get("username");
        String password = parameters.get("password");

        String clientId = client.getClientId();
        parameters.put("clientId", clientId);

        PrincipleDTO principleDTO = PrincipleDTO.builder()
                .principle(StringUtils.isEmpty(username) ? parameters.get("mobile") : username)
                .principleType(StringUtils.isEmpty(username) ? PrincipleTypeEnum.MOBILE : PrincipleTypeEnum.USERNAME)
                .build();

        //校验用户基本信息
        UsernamePasswordAuthenticationToken userAuth = new UsernamePasswordAuthenticationToken(principleDTO, password, null);
        log.debug("userAuth: {}", userAuth);
        userAuth.setDetails(parameters);
        UsernamePasswordAuthenticationToken newAuth =
                userAuthenticationCommonService.authenticateUserInfo(userAuth);

        UserExpandDto userExpandDto = (UserExpandDto) newAuth.getDetails();
        //在公众号通过账号密码登陆时，需要绑定微信用户
        if (!StringUtils.isEmpty(parameters.get("openId"))) {
            var rayClientDetail = (RayClientDetail) client;
            //校验应用微信配置信息是否完整
            userAuthenticationCommonService.checkWxAppConfig(rayClientDetail);
            //绑定微信用户
            userAuthenticationCommonService.bindAccountUser2Wx(userExpandDto.getId(), parameters.get("openId"),
                    rayClientDetail.getWechatApplicationCode());
        }

        if (parameters.containsKey("extendparam")) {
            userAuthenticationCommonService.bindBankJiUser(parameters.get("extendparam"), userExpandDto.getId());
        }
        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, newAuth);
    }
}
