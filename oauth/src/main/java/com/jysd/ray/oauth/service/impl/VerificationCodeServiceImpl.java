package com.jysd.ray.oauth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.joinus.ray.lib.redis.util.AuthRedisUtil;
import com.jysd.ray.oauth.constant.CommonConstant;
import com.jysd.ray.oauth.dto.SmsRequestDTO;
import com.jysd.ray.oauth.enums.ClientVerificationCodeEnum;
import com.jysd.ray.oauth.param.VerificationCodeParam;
import com.jysd.ray.oauth.service.MiddlewareService;
import com.jysd.ray.oauth.service.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class VerificationCodeServiceImpl implements VerificationCodeService {


    @Autowired
    private MiddlewareService middlewareService;
    @Autowired
    private AuthRedisUtil authRedisUtil;
    @Value("${is.production.environment:true}")
    private Boolean isProdEnv;

    @Override
    public String sendVerificationCode(VerificationCodeParam param, ClientVerificationCodeEnum clientVerificationCodeEnum) {
        SmsRequestDTO smsRequestDTO = BeanUtil.copyProperties(param, SmsRequestDTO.class);
        smsRequestDTO = smsRequestDTO.toBuilder()
                .appCode(clientVerificationCodeEnum.getAppCode())
                .templateId(clientVerificationCodeEnum.getTemplateId())
                .templateContentParam(clientVerificationCodeEnum.getTemplateContent())
                .extraData("5分钟")
                .phoneNum(param.getMobile())
                .isH5Scene(1)
                .yzmType(1)
                .build();
        if (!isProdEnv) {
            //测试环境自动生成4位随机数字的验证码，并以手机号位key存入redis中
            String verificationCode = String.valueOf((int) ((Math.random() * 9 + 1) * 1000));
            authRedisUtil.setEx(CommonConstant.REDIS_KEY.CUSTOMIZED_VERIFICATION_CODE + param.getMobile(), verificationCode, 5, TimeUnit.MINUTES);
            return verificationCode;
        }
        middlewareService.sendVerificationCode(smsRequestDTO);
        return null;
    }

    @Override
    public boolean verifyCode(String mobile, String code, ClientVerificationCodeEnum clientVerificationCodeEnum) {
        if (!isProdEnv) {
            return code.equals(authRedisUtil.get(CommonConstant.REDIS_KEY.CUSTOMIZED_VERIFICATION_CODE + mobile));
        }
        SmsRequestDTO smsRequestDTO = SmsRequestDTO.builder()
                .phoneNum(mobile)
                .appCode(clientVerificationCodeEnum.getAppCode())
                .yzmType(1)
                .toBeVerifiedYzm(code)
                .build();
        return middlewareService.verifyCode(smsRequestDTO);
    }



}
