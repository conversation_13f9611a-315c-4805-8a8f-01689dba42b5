package com.jysd.ray.oauth.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 10:44
 * @Version 1.0
 */
@FeignClient(value = "RAY-SCHOOL-API")
public interface SchoolFeign {
	/**
	 * 根据用户ID查询学校ID集合（isAdmin=true）
	 * @param userId  用户ID
 	 * @return  学校ID集合
	 */
	@GetMapping("/schools/admin-school-ids-by-user-id")
	List<Long> listIsAdminSchoolIdsByUserId(@RequestParam("userId") Long userId);
}
