package com.jysd.ray.oauth.dto;

import lombok.*;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * UserVo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegisterDto {

    private Long id;

    private String username;

    private String name;
    private String mobile;
    private String idCard;
    private Boolean locked;

    private Long defaultStudentId;

    private Long mainParentId;

    private Long defaultSchoolId;

    private Integer identity;
    private String ethnicGroup;
    private Date birthday;
    private String place;
    private String email;
    private String avatarUrl;

    private String idCardPicUrl;


    private String thirdId;

    private String thirdExtraId;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}