package com.jysd.ray.oauth.dto;

import com.jysd.ray.oauth.enums.DataActionTypeEnum;
import com.jysd.ray.oauth.lib.enums.UserIdentityEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UserIdentityDto {

    private UserIdentityEnum userIdentity;

    private DataActionTypeEnum identityActionType;
}
