package com.jysd.ray.oauth.controller;

import com.jysd.ray.oauth.dto.DeviceInfoDto;
import com.jysd.ray.oauth.service.DeviceManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备管理控制器
 * 提供用户设备管理相关的API
 */
@Slf4j
@RestController
@RequestMapping("/devices")
public class DeviceManagementController {

    @Resource
    private DeviceManagementService deviceManagementService;

    /**
     * 获取当前用户的所有已登录设备
     */
    @GetMapping
    public List<DeviceInfoDto> getUserDevices(OAuth2Authentication authentication) {
        String username = authentication.getName();
        String clientId = authentication.getOAuth2Request().getClientId();
        
        log.info("Getting devices for user: {} in client: {}", username, clientId);
        return deviceManagementService.getUserDevices(clientId, username);
    }

    /**
     * 踢出指定设备
     */
    @DeleteMapping("/{deviceId}")
    public void removeDevice(@PathVariable String deviceId, OAuth2Authentication authentication) {
        String username = authentication.getName();
        String clientId = authentication.getOAuth2Request().getClientId();
        
        log.info("Removing device: {} for user: {} in client: {}", deviceId, username, clientId);
        deviceManagementService.removeDevice(clientId, username, deviceId);
    }

    /**
     * 踢出所有其他设备（保留当前设备）
     */
    @DeleteMapping("/others")
    public void removeOtherDevices(OAuth2Authentication authentication) {
        String username = authentication.getName();
        String clientId = authentication.getOAuth2Request().getClientId();
        
        // 获取当前设备ID
        String currentDeviceId = authentication.getOAuth2Request().getRequestParameters().get("deviceId");
        
        log.info("Removing other devices for user: {} in client: {}, keeping device: {}", 
                username, clientId, currentDeviceId);
        deviceManagementService.removeOtherDevices(clientId, username, currentDeviceId);
    }

    /**
     * 踢出所有设备
     */
    @DeleteMapping
    public void removeAllDevices(OAuth2Authentication authentication) {
        String username = authentication.getName();
        String clientId = authentication.getOAuth2Request().getClientId();
        
        log.info("Removing all devices for user: {} in client: {}", username, clientId);
        deviceManagementService.removeAllDevices(clientId, username);
    }
}
