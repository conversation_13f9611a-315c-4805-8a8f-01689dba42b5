package com.jysd.ray.oauth.service;

import com.jysd.ray.oauth.dto.DeviceInfoDto;

import java.util.List;

/**
 * 设备管理服务接口
 */
public interface DeviceManagementService {
    
    /**
     * 获取用户的所有已登录设备
     * 
     * @param clientId 客户端ID
     * @param username 用户名
     * @return 设备信息列表
     */
    List<DeviceInfoDto> getUserDevices(String clientId, String username);
    
    /**
     * 移除指定设备的token
     * 
     * @param clientId 客户端ID
     * @param username 用户名
     * @param deviceId 设备ID
     */
    void removeDevice(String clientId, String username, String deviceId);
    
    /**
     * 移除除指定设备外的所有其他设备
     * 
     * @param clientId 客户端ID
     * @param username 用户名
     * @param keepDeviceId 要保留的设备ID
     */
    void removeOtherDevices(String clientId, String username, String keepDeviceId);
    
    /**
     * 移除用户的所有设备token
     * 
     * @param clientId 客户端ID
     * @param username 用户名
     */
    void removeAllDevices(String clientId, String username);
}
