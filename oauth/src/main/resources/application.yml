server:
  port: 9000
spring:
  application:
    name: ray-oauth
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PWD:123456}
mybatis-plus:
  mapper-locations: classpath*:/mappers/**/*Mapper.xml
  type-enums-package: com.jysd.ray.**.enums
ray:
  redis:
    auth:
      active: true
      database: 11
eureka:
  client:
    healthcheck:
      enabled: true
  instance:
    initial-status: starting