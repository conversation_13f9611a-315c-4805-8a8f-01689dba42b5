package com.jysd.ray.oauth;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 多设备登录功能测试
 */
@SpringBootTest
@AutoConfigureTestMvc
public class MultiDeviceLoginTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试多设备登录功能
     */
    @Test
    public void testMultiDeviceLogin() throws Exception {
        String mobile = "15038203788"; // 使用测试手机号
        String code = "967111"; // 使用测试验证码
        String clientId = "test_client";
        String clientSecret = "test_secret";

        // 设备1登录
        String device1Id = "PC_001";
        MvcResult result1 = mockMvc.perform(post("/token")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("grant_type", "verification_code")
                .param("mobile", mobile)
                .param("code", code)
                .param("deviceId", device1Id)
                .param("osInfo", "Windows 10")
                .param("browserInfo", "Chrome 91.0")
                .param("ipAddress", "*************")
                .param("client_id", clientId)
                .param("client_secret", clientSecret))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andReturn();

        String response1 = result1.getResponse().getContentAsString();
        System.out.println("Device 1 login response: " + response1);

        // 设备2登录
        String device2Id = "PC_002";
        MvcResult result2 = mockMvc.perform(post("/token")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("grant_type", "verification_code")
                .param("mobile", mobile)
                .param("code", code)
                .param("deviceId", device2Id)
                .param("osInfo", "macOS 11.0")
                .param("browserInfo", "Safari 14.0")
                .param("ipAddress", "*************")
                .param("client_id", clientId)
                .param("client_secret", clientSecret))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andReturn();

        String response2 = result2.getResponse().getContentAsString();
        System.out.println("Device 2 login response: " + response2);

        // 验证两个设备的token都有效
        // 这里需要实际的token验证逻辑
    }

    /**
     * 测试相同设备重复登录
     */
    @Test
    public void testSameDeviceReLogin() throws Exception {
        String mobile = "15038203788";
        String code = "967111";
        String clientId = "test_client";
        String clientSecret = "test_secret";
        String deviceId = "PC_001";

        // 第一次登录
        MvcResult result1 = mockMvc.perform(post("/token")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("grant_type", "verification_code")
                .param("mobile", mobile)
                .param("code", code)
                .param("deviceId", deviceId)
                .param("client_id", clientId)
                .param("client_secret", clientSecret))
                .andExpect(status().isOk())
                .andReturn();

        String token1 = extractTokenFromResponse(result1.getResponse().getContentAsString());

        // 第二次登录（相同设备）
        MvcResult result2 = mockMvc.perform(post("/token")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("grant_type", "verification_code")
                .param("mobile", mobile)
                .param("code", code)
                .param("deviceId", deviceId)
                .param("client_id", clientId)
                .param("client_secret", clientSecret))
                .andExpect(status().isOk())
                .andReturn();

        String token2 = extractTokenFromResponse(result2.getResponse().getContentAsString());

        // 验证第一个token应该失效，第二个token有效
        // 这里需要实际的token验证逻辑
    }

    /**
     * 测试不提供设备ID的情况（向后兼容）
     */
    @Test
    public void testLoginWithoutDeviceId() throws Exception {
        String mobile = "15038203788";
        String code = "967111";
        String clientId = "test_client";
        String clientSecret = "test_secret";

        // 不提供设备ID的登录
        MvcResult result = mockMvc.perform(post("/token")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("grant_type", "verification_code")
                .param("mobile", mobile)
                .param("code", code)
                .param("client_id", clientId)
                .param("client_secret", clientSecret))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andReturn();

        String response = result.getResponse().getContentAsString();
        System.out.println("Login without deviceId response: " + response);
    }

    /**
     * 从响应中提取access_token
     */
    private String extractTokenFromResponse(String response) throws Exception {
        return objectMapper.readTree(response).get("access_token").asText();
    }
}
