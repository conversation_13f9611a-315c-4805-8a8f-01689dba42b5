package com.jysd.ray.oauth;

import org.junit.Test;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class PasswordTestTool {
    @Test
    public void passwordTestTool() {
        String key = "jy0:123456";
        Base64.Encoder encoder = Base64.getEncoder();
        String result = encoder.encodeToString(key.getBytes(StandardCharsets.UTF_8));
        System.out.println("Base64 encode result = " + result);
        BCryptPasswordEncoder bcryptEncoder = new BCryptPasswordEncoder();
        String bcryptPassword = bcryptEncoder.encode("123456");
        System.out.println("bcryptPassword = " + bcryptPassword);
        boolean compareResult = bcryptEncoder.matches("123456", "$2a$10$CwTOPyFxcm0d2dGyE03h6ODaPFluWHtFyCpTYKRky/iQA2p/nWwqe");
        System.out.println("compareResult = " + compareResult);
    }
}
