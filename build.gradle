bootJar {
    enabled = false
}

jar {
    enabled = true
}

dependencies {
    compile 'com.joinus.ray.lib:lib-redis:1.3.7'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-oauth2'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation "com.ctrip.framework.apollo:apollo-client:1.4.0"
    implementation 'com.baomidou:mybatis-plus-boot-starter:3.2.0'
    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'
    runtimeOnly "org.springframework.boot:spring-boot-devtools"
    runtimeOnly 'mysql:mysql-connector-java:8.0.16'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}