plugins {
    id 'org.springframework.boot' version '2.2.8.RELEASE'
    id 'io.spring.dependency-management' version '1.0.9.RELEASE'
    id "com.palantir.docker" version "0.22.1" apply false
    id 'java'
}

ext {
    set('springCloudVersion', "Hoxton.SR5")
    set('springBootVersion', '2.2.8.RELEASE')
}

allprojects {

    apply plugin: 'application'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'maven-publish'
    apply plugin: 'com.palantir.docker'

    group = 'com.jysd.ray'
    version = '0.0.1'
    sourceCompatibility = '11'

    configurations {
        developmentOnly
        runtimeClasspath {
            extendsFrom developmentOnly
        }
        compileOnly {
            extendsFrom annotationProcessor
        }
    }

    repositories {
        mavenCentral()
    }

    dependencyManagement {
        imports {
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        }
    }

    dependencies {
        implementation "cn.hutool:hutool-all:5.6.2"
    }
}

def getMavenUrl() {
    if (isSnapShot()) {
        return project.properties.get("snapUrl")
    } else {
        return project.properties.get("mavenUrl")
    }
}

def getMavenUserName() {
    return project.properties.get("mavenUserName")
}

def getMavenUserPwd() {
    return project.properties.get("mavenUserPwd")
}

def isSnapShot() {
    return project.properties.containsKey("snapShot") &&
            project.properties.get("snapShot").toBoolean()
}

publishing {
    repositories {
        maven {
            url getMavenUrl()
            credentials {
                username getMavenUserName()
                password getMavenUserPwd()

            }
        }
    }
    publications {
        maven(MavenPublication) {
            from components.java
        }
    }
}

compileJava.dependsOn(processResources)
