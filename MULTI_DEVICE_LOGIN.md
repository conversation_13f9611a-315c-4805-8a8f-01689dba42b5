# 多设备登录功能说明

## 功能概述

本功能允许同一个账号在多个PC端同时登录而不被顶掉。通过在登录请求中添加设备标识（deviceId），系统可以区分不同的设备，从而实现多设备并发登录。

## 实现原理

1. **设备标识**: 客户端在登录时提供唯一的设备ID（deviceId）
2. **Token管理**: 系统根据设备ID管理token，只清除相同设备的旧token
3. **向后兼容**: 如果不提供设备ID，系统保持原有行为（清除所有token）

## 使用方法

### 1. 客户端登录请求

在使用`VerificationCodeTokenGranter`进行验证码登录时，需要在请求参数中添加`deviceId`：

```http
POST /token
Content-Type: application/x-www-form-urlencoded

grant_type=verification_code&
mobile=13800138000&
code=123456&
deviceId=PC_001&
client_id=your_client_id&
client_secret=your_client_secret
```

### 2. 参数说明

- `deviceId`: 设备唯一标识符，建议格式：`PC_设备编号` 或 `MAC地址` 等
- 其他参数保持不变

### 3. 设备ID生成建议

```javascript
// 前端生成设备ID示例
function generateDeviceId() {
    // 方案1: 使用MAC地址 + 时间戳
    const macAddress = getMacAddress(); // 需要实现获取MAC地址的方法
    const timestamp = Date.now();
    return `PC_${macAddress}_${timestamp}`;
    
    // 方案2: 使用浏览器指纹
    const fingerprint = generateBrowserFingerprint();
    return `PC_${fingerprint}`;
    
    // 方案3: 使用UUID + 本地存储
    let deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
        deviceId = `PC_${generateUUID()}`;
        localStorage.setItem('deviceId', deviceId);
    }
    return deviceId;
}
```

## 行为说明

### 1. 提供设备ID的情况

- 系统只会清除**相同设备ID**的旧token
- 不同设备ID的token可以同时存在
- 实现真正的多设备并发登录

### 2. 不提供设备ID的情况

- 系统清除该用户的**所有token**
- 保持原有的单设备登录行为
- 确保向后兼容性

## 配置选项

可以通过配置文件控制多设备登录的行为：

```yaml
# application.yml
ray-oauth:
  multi-device:
    enabled: true  # 是否启用多设备登录
    max-devices: 5  # 每个用户最大设备数（可选，未实现）
```

## 安全考虑

1. **设备ID唯一性**: 确保设备ID在用户范围内唯一
2. **设备ID保护**: 避免设备ID被恶意获取或伪造
3. **Token管理**: 定期清理过期的token
4. **设备管理**: 可以考虑添加设备管理功能，允许用户查看和管理已登录的设备

## 扩展功能

### 1. 设备管理接口

可以添加以下接口来管理用户的登录设备：

- `GET /devices` - 获取用户已登录的设备列表
- `DELETE /devices/{deviceId}` - 踢出指定设备
- `DELETE /devices` - 踢出所有设备

### 2. 设备信息增强

可以在token中包含更多设备信息：

- 设备类型（PC、Mobile、Tablet）
- 操作系统信息
- 浏览器信息
- 登录时间
- 最后活跃时间

## 测试用例

### 1. 多设备登录测试

```bash
# 设备1登录
curl -X POST http://localhost:9000/token \
  -d "grant_type=verification_code&mobile=13800138000&code=123456&deviceId=PC_001&client_id=test&client_secret=test"

# 设备2登录
curl -X POST http://localhost:9000/token \
  -d "grant_type=verification_code&mobile=13800138000&code=123456&deviceId=PC_002&client_id=test&client_secret=test"

# 验证两个设备的token都有效
```

### 2. 相同设备重复登录测试

```bash
# 设备1第一次登录
curl -X POST http://localhost:9000/token \
  -d "grant_type=verification_code&mobile=13800138000&code=123456&deviceId=PC_001&client_id=test&client_secret=test"

# 设备1第二次登录（应该清除第一次的token）
curl -X POST http://localhost:9000/token \
  -d "grant_type=verification_code&mobile=13800138000&code=123456&deviceId=PC_001&client_id=test&client_secret=test"
```

## 注意事项

1. 确保客户端能够持久化保存设备ID
2. 设备ID应该在应用卸载重装后保持一致（如果需要）
3. 考虑设备ID的隐私保护
4. 监控多设备登录的使用情况，防止滥用
